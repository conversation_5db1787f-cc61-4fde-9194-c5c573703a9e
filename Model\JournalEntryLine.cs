using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace JayERP.Model
{
    public class JournalEntryLine : BaseEntity
    {
        [Required]
        public int JournalEntryId { get; set; }
        
        [Required]
        public int AccountId { get; set; }
        
        [Range(1, int.MaxValue)]
        public int LineNumber { get; set; }
        
        [MaxLength(500)]
        public string Description { get; set; } = string.Empty;
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal DebitAmount { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal CreditAmount { get; set; }
        
        [MaxLength(100)]
        public string CostCenter { get; set; } = string.Empty;
        
        [MaxLength(100)]
        public string Department { get; set; } = string.Empty;
        
        [MaxLength(100)]
        public string Project { get; set; } = string.Empty;
        
        [MaxLength(500)]
        public string Notes { get; set; } = string.Empty;
        
        // Navigation Properties
        [ForeignKey("JournalEntryId")]
        public virtual JournalEntry JournalEntry { get; set; } = null!;
        
        [ForeignKey("AccountId")]
        public virtual ChartOfAccounts Account { get; set; } = null!;
    }
}
