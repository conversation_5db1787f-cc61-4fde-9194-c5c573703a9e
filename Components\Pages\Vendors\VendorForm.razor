@using Syncfusion.Blazor.Inputs
@using Syncfusion.Blazor.DropDowns
@using Syncfusion.Blazor.Buttons
@inject IVendorService VendorService
@inject IJSRuntime JSRuntime

<EditForm Model="@vendorModel" OnValidSubmit="@HandleValidSubmit">
    <DataAnnotationsValidator />

    <div class="row">
        <div class="col-md-6">
            <div class="mb-3">
                <SfTextBox @bind-Value="vendorModel.Name"
                          Placeholder="Vendor Name *"
                          FloatLabelType="FloatLabelType.Auto"
                          ShowClearButton="true">
                </SfTextBox>
                <ValidationMessage For="@(() => vendorModel.Name)" class="text-danger" />
            </div>
        </div>
        <div class="col-md-6">
            <div class="mb-3">
                <SfTextBox @bind-Value="vendorModel.CompanyName"
                          Placeholder="Company Name *"
                          FloatLabelType="FloatLabelType.Auto"
                          ShowClearButton="true">
                </SfTextBox>
                <ValidationMessage For="@(() => vendorModel.CompanyName)" class="text-danger" />
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-6">
            <div class="mb-3">
                <SfDropDownList TValue="VendorCategory" TItem="CategoryOption"
                               @bind-Value="vendorModel.Category"
                               DataSource="@categoryOptions"
                               Placeholder="Category *"
                               FloatLabelType="FloatLabelType.Auto">
                    <DropDownListFieldSettings Text="Text" Value="Value"></DropDownListFieldSettings>
                </SfDropDownList>
                <ValidationMessage For="@(() => vendorModel.Category)" class="text-danger" />
            </div>
        </div>
        <div class="col-md-6">
            <div class="mb-3">
                <SfDropDownList TValue="PaymentTerms" TItem="PaymentTermsOption"
                               @bind-Value="vendorModel.PaymentTerms"
                               DataSource="@paymentTermsOptions"
                               Placeholder="Payment Terms *"
                               FloatLabelType="FloatLabelType.Auto">
                    <DropDownListFieldSettings Text="Text" Value="Value"></DropDownListFieldSettings>
                </SfDropDownList>
                <ValidationMessage For="@(() => vendorModel.PaymentTerms)" class="text-danger" />
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <div class="mb-3">
                <SfTextBox @bind-Value="vendorModel.Address"
                          Placeholder="Address *"
                          FloatLabelType="FloatLabelType.Auto"
                          Multiline="true"
                          ShowClearButton="true">
                </SfTextBox>
                <ValidationMessage For="@(() => vendorModel.Address)" class="text-danger" />
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-4">
            <div class="mb-3">
                <SfTextBox @bind-Value="vendorModel.City"
                          Placeholder="City"
                          FloatLabelType="FloatLabelType.Auto"
                          ShowClearButton="true">
                </SfTextBox>
            </div>
        </div>
        <div class="col-md-4">
            <div class="mb-3">
                <SfTextBox @bind-Value="vendorModel.State"
                          Placeholder="State"
                          FloatLabelType="FloatLabelType.Auto"
                          ShowClearButton="true">
                </SfTextBox>
            </div>
        </div>
        <div class="col-md-4">
            <div class="mb-3">
                <SfTextBox @bind-Value="vendorModel.PostalCode"
                          Placeholder="Postal Code"
                          FloatLabelType="FloatLabelType.Auto"
                          ShowClearButton="true">
                </SfTextBox>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-6">
            <div class="mb-3">
                <SfTextBox @bind-Value="vendorModel.Email"
                          Placeholder="Email *"
                          FloatLabelType="FloatLabelType.Auto"
                          Type="InputType.Email"
                          ShowClearButton="true">
                </SfTextBox>
                <ValidationMessage For="@(() => vendorModel.Email)" class="text-danger" />
            </div>
        </div>
        <div class="col-md-6">
            <div class="mb-3">
                <SfTextBox @bind-Value="vendorModel.Phone"
                          Placeholder="Phone"
                          FloatLabelType="FloatLabelType.Auto"
                          Type="InputType.Tel"
                          ShowClearButton="true">
                </SfTextBox>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-6">
            <div class="mb-3">
                <SfTextBox @bind-Value="vendorModel.Mobile"
                          Placeholder="Mobile"
                          FloatLabelType="FloatLabelType.Auto"
                          Type="InputType.Tel"
                          ShowClearButton="true">
                </SfTextBox>
            </div>
        </div>
        <div class="col-md-6">
            <div class="mb-3">
                <SfTextBox @bind-Value="vendorModel.ContactPerson"
                          Placeholder="Contact Person"
                          FloatLabelType="FloatLabelType.Auto"
                          ShowClearButton="true">
                </SfTextBox>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-6">
            <div class="mb-3">
                <SfTextBox @bind-Value="vendorModel.TaxNumber"
                          Placeholder="Tax Number"
                          FloatLabelType="FloatLabelType.Auto"
                          ShowClearButton="true">
                </SfTextBox>
            </div>
        </div>
        <div class="col-md-6">
            <div class="mb-3">
                <SfNumericTextBox TValue="decimal" @bind-Value="vendorModel.CreditLimit"
                                 Placeholder="Credit Limit"
                                 FloatLabelType="FloatLabelType.Auto"
                                 Format="C2"
                                 Min="0"
                                 ShowClearButton="true">
                </SfNumericTextBox>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-6">
            <div class="mb-3">
                <SfNumericTextBox TValue="int" @bind-Value="vendorModel.QualityRating"
                                 Placeholder="Quality Rating (1-5)"
                                 FloatLabelType="FloatLabelType.Auto"
                                 Min="1"
                                 Max="5"
                                 Step="1"
                                 ShowClearButton="true">
                </SfNumericTextBox>
            </div>
        </div>
        <div class="col-md-6">
            <div class="mb-3">
                <SfNumericTextBox TValue="int" @bind-Value="vendorModel.DeliveryRating"
                                 Placeholder="Delivery Rating (1-5)"
                                 FloatLabelType="FloatLabelType.Auto"
                                 Min="1"
                                 Max="5"
                                 Step="1"
                                 ShowClearButton="true">
                </SfNumericTextBox>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <div class="mb-3">
                <SfTextBox @bind-Value="vendorModel.Notes"
                          Placeholder="Notes"
                          FloatLabelType="FloatLabelType.Auto"
                          Multiline="true"
                          ShowClearButton="true">
                </SfTextBox>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <div class="d-flex justify-content-end gap-2">
                <SfButton @onclick="OnCancelClick"
                         CssClass="e-outline">
                    Cancel
                </SfButton>
                <SfButton ButtonType="ButtonType.Submit"
                         IsPrimary="true"
                         Disabled="@isSubmitting">
                    @if (isSubmitting)
                    {
                        <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                    }
                    @(IsEditMode ? "Update" : "Save")
                </SfButton>
            </div>
        </div>
    </div>
</EditForm>

@code {
    [Parameter] public VendorDto? Vendor { get; set; }
    [Parameter] public EventCallback OnSave { get; set; }
    [Parameter] public EventCallback OnCancel { get; set; }

    private VendorCreateDto vendorModel = new();
    private bool isSubmitting = false;
    private bool IsEditMode => Vendor != null;

    private List<CategoryOption> categoryOptions = new()
    {
        new CategoryOption { Text = "Ingredients", Value = VendorCategory.Ingredients },
        new CategoryOption { Text = "Packaging", Value = VendorCategory.Packaging },
        new CategoryOption { Text = "Supplies", Value = VendorCategory.Supplies },
        new CategoryOption { Text = "Services", Value = VendorCategory.Services }
    };

    private List<PaymentTermsOption> paymentTermsOptions = new()
    {
        new PaymentTermsOption { Text = "NET 30", Value = PaymentTerms.Net30 },
        new PaymentTermsOption { Text = "NET 15", Value = PaymentTerms.Net15 },
        new PaymentTermsOption { Text = "NET 7", Value = PaymentTerms.Net7 },
        new PaymentTermsOption { Text = "Advance Payment", Value = PaymentTerms.AdvancePayment },
        new PaymentTermsOption { Text = "Cash on Delivery", Value = PaymentTerms.CashOnDelivery },
        new PaymentTermsOption { Text = "Immediate", Value = PaymentTerms.Immediate }
    };

    protected override void OnParametersSet()
    {
        if (Vendor != null)
        {
            vendorModel = new VendorCreateDto
            {
                Name = Vendor.Name,
                CompanyName = Vendor.CompanyName,
                Category = Vendor.Category,
                Address = Vendor.Address,
                City = Vendor.City,
                State = Vendor.State,
                PostalCode = Vendor.PostalCode,
                Country = Vendor.Country,
                Email = Vendor.Email,
                Phone = Vendor.Phone,
                Mobile = Vendor.Mobile,
                ContactPerson = Vendor.ContactPerson,
                TaxNumber = Vendor.TaxNumber,
                RegistrationNumber = Vendor.RegistrationNumber,
                PaymentTerms = Vendor.PaymentTerms,
                CreditLimit = Vendor.CreditLimit,
                QualityRating = Vendor.QualityRating,
                DeliveryRating = Vendor.DeliveryRating,
                Notes = Vendor.Notes
            };
        }
        else
        {
            vendorModel = new VendorCreateDto();
        }
    }

    private async Task HandleValidSubmit()
    {
        isSubmitting = true;
        try
        {
            if (IsEditMode)
            {
                var updateDto = new VendorUpdateDto
                {
                    Id = Vendor!.Id,
                    Name = vendorModel.Name,
                    CompanyName = vendorModel.CompanyName,
                    Category = vendorModel.Category,
                    Address = vendorModel.Address,
                    City = vendorModel.City,
                    State = vendorModel.State,
                    PostalCode = vendorModel.PostalCode,
                    Country = vendorModel.Country,
                    Email = vendorModel.Email,
                    Phone = vendorModel.Phone,
                    Mobile = vendorModel.Mobile,
                    ContactPerson = vendorModel.ContactPerson,
                    TaxNumber = vendorModel.TaxNumber,
                    RegistrationNumber = vendorModel.RegistrationNumber,
                    PaymentTerms = vendorModel.PaymentTerms,
                    CreditLimit = vendorModel.CreditLimit,
                    QualityRating = vendorModel.QualityRating,
                    DeliveryRating = vendorModel.DeliveryRating,
                    Notes = vendorModel.Notes,
                    IsActive = Vendor.IsActive
                };

                await VendorService.UpdateVendorAsync(updateDto, "CurrentUser"); // TODO: Get actual user
            }
            else
            {
                await VendorService.CreateVendorAsync(vendorModel, "CurrentUser"); // TODO: Get actual user
            }

            await OnSave.InvokeAsync();
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"Error saving vendor: {ex.Message}");
        }
        finally
        {
            isSubmitting = false;
        }
    }

    private async Task OnCancelClick()
    {
        await OnCancel.InvokeAsync();
    }

    private string GetValidationClass(string fieldName)
    {
        // This would typically check validation state
        return string.Empty;
    }

    public class CategoryOption
    {
        public string Text { get; set; } = string.Empty;
        public VendorCategory Value { get; set; }
    }

    public class PaymentTermsOption
    {
        public string Text { get; set; } = string.Empty;
        public PaymentTerms Value { get; set; }
    }
}
