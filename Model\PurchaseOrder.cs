using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace JayERP.Model
{
    public class PurchaseOrder : BaseEntity
    {
        [Required]
        [MaxLength(50)]
        public string OrderNumber { get; set; } = string.Empty;
        
        [Required]
        public int VendorId { get; set; }
        
        public DateTime OrderDate { get; set; } = DateTime.UtcNow;
        
        public DateTime? ExpectedDeliveryDate { get; set; }
        
        public PurchaseOrderStatus Status { get; set; } = PurchaseOrderStatus.Draft;
        
        public PaymentTerms PaymentTerms { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal SubTotal { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal TaxAmount { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal DiscountAmount { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalAmount { get; set; }
        
        [MaxLength(100)]
        public string ApprovedBy { get; set; } = string.Empty;
        
        public DateTime? ApprovedDate { get; set; }
        
        [MaxLength(1000)]
        public string Notes { get; set; } = string.Empty;
        
        [MaxLength(500)]
        public string DeliveryAddress { get; set; } = string.Empty;
        
        // Navigation Properties
        [ForeignKey("VendorId")]
        public virtual Vendor Vendor { get; set; } = null!;
        
        public virtual ICollection<PurchaseOrderItem> Items { get; set; } = new List<PurchaseOrderItem>();
        public virtual ICollection<GoodsReceipt> GoodsReceipts { get; set; } = new List<GoodsReceipt>();
    }
}
