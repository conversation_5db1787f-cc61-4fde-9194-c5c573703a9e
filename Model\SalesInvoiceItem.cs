using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace JayERP.Model
{
    public class SalesInvoiceItem : BaseEntity
    {
        [Required]
        public int SalesInvoiceId { get; set; }
        
        [Required]
        public int ProductId { get; set; }
        
        [Range(0, double.MaxValue)]
        public decimal Quantity { get; set; }
        
        [Range(0, double.MaxValue)]
        public decimal UnitPrice { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal LineTotal { get; set; }
        
        [Range(0, 100)]
        public decimal DiscountPercentage { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal DiscountAmount { get; set; }
        
        [Range(0, 100)]
        public decimal TaxRate { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal TaxAmount { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal NetAmount { get; set; }
        
        [MaxLength(50)]
        public string BatchNumber { get; set; } = string.Empty;
        
        public DateTime? ExpiryDate { get; set; }
        
        [MaxLength(500)]
        public string Description { get; set; } = string.Empty;
        
        // Navigation Properties
        [ForeignKey("SalesInvoiceId")]
        public virtual SalesInvoice SalesInvoice { get; set; } = null!;
        
        [ForeignKey("ProductId")]
        public virtual Product Product { get; set; } = null!;
    }
}
