using JayERP.Data;
using JayERP.DTOs;
using JayERP.Model;
using Microsoft.EntityFrameworkCore;

namespace JayERP.Services
{
    public class PurchaseInvoiceService : IPurchaseInvoiceService
    {
        private readonly ApplicationDbContext _context;

        public PurchaseInvoiceService(ApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<IEnumerable<PurchaseInvoiceListDto>> GetAllPurchaseInvoicesAsync()
        {
            return await _context.PurchaseInvoices
                .Include(pi => pi.Vendor)
                .Include(pi => pi.PurchaseOrder)
                .Include(pi => pi.GoodsReceipt)
                .Include(pi => pi.Items)
                .Where(pi => pi.IsActive)
                .Select(pi => new PurchaseInvoiceListDto
                {
                    Id = pi.Id,
                    InvoiceNumber = pi.InvoiceNumber,
                    VendorName = pi.Vendor.Name,
                    VendorInvoiceNumber = pi.VendorInvoiceNumber,
                    InvoiceDate = pi.InvoiceDate,
                    DueDate = pi.DueDate,
                    Status = pi.Status.ToString(),
                    PaymentTerms = pi.PaymentTerms.ToString(),
                    TotalAmount = pi.TotalAmount,
                    PaidAmount = pi.PaidAmount,
                    BalanceAmount = pi.BalanceAmount,
                    PurchaseOrderNumber = pi.PurchaseOrder != null ? pi.PurchaseOrder.OrderNumber : "",
                    GoodsReceiptNumber = pi.GoodsReceipt != null ? pi.GoodsReceipt.ReceiptNumber : "",
                    IsActive = pi.IsActive,
                    CreatedDate = pi.CreatedDate,
                    ItemCount = pi.Items.Count,
                    DaysOverdue = pi.Status == InvoiceStatus.Overdue ? (int)(DateTime.UtcNow - pi.DueDate).TotalDays : 0
                })
                .OrderByDescending(pi => pi.CreatedDate)
                .ToListAsync();
        }

        public async Task<PurchaseInvoiceDto?> GetPurchaseInvoiceByIdAsync(int id)
        {
            var purchaseInvoice = await _context.PurchaseInvoices
                .Include(pi => pi.Vendor)
                .Include(pi => pi.PurchaseOrder)
                .Include(pi => pi.GoodsReceipt)
                .Include(pi => pi.Items)
                    .ThenInclude(item => item.Material)
                .FirstOrDefaultAsync(pi => pi.Id == id && pi.IsActive);

            if (purchaseInvoice == null) return null;

            return new PurchaseInvoiceDto
            {
                Id = purchaseInvoice.Id,
                InvoiceNumber = purchaseInvoice.InvoiceNumber,
                VendorId = purchaseInvoice.VendorId,
                VendorName = purchaseInvoice.Vendor.Name,
                PurchaseOrderId = purchaseInvoice.PurchaseOrderId,
                PurchaseOrderNumber = purchaseInvoice.PurchaseOrder?.OrderNumber ?? "",
                GoodsReceiptId = purchaseInvoice.GoodsReceiptId,
                GoodsReceiptNumber = purchaseInvoice.GoodsReceipt?.ReceiptNumber ?? "",
                InvoiceDate = purchaseInvoice.InvoiceDate,
                DueDate = purchaseInvoice.DueDate,
                Status = purchaseInvoice.Status,
                VendorInvoiceNumber = purchaseInvoice.VendorInvoiceNumber,
                PaymentTerms = purchaseInvoice.PaymentTerms,
                SubTotal = purchaseInvoice.SubTotal,
                TaxAmount = purchaseInvoice.TaxAmount,
                DiscountAmount = purchaseInvoice.DiscountAmount,
                TotalAmount = purchaseInvoice.TotalAmount,
                PaidAmount = purchaseInvoice.PaidAmount,
                BalanceAmount = purchaseInvoice.BalanceAmount,
                Notes = purchaseInvoice.Notes,
                IsActive = purchaseInvoice.IsActive,
                CreatedDate = purchaseInvoice.CreatedDate,
                ModifiedDate = purchaseInvoice.ModifiedDate,
                CreatedBy = purchaseInvoice.CreatedBy,
                ModifiedBy = purchaseInvoice.ModifiedBy,
                Items = purchaseInvoice.Items.Select(item => new PurchaseInvoiceItemDto
                {
                    Id = item.Id,
                    PurchaseInvoiceId = item.PurchaseInvoiceId,
                    MaterialId = item.MaterialId,
                    MaterialName = item.Material.Name,
                    MaterialCode = item.Material.Code,
                    Quantity = item.Quantity,
                    UnitPrice = item.UnitPrice,
                    LineTotal = item.LineTotal,
                    TaxRate = item.TaxRate,
                    TaxAmount = item.TaxAmount,
                    DiscountRate = item.DiscountRate,
                    DiscountAmount = item.DiscountAmount,
                    NetAmount = item.NetAmount,
                    Description = item.Description
                }).ToList()
            };
        }

        public async Task<PurchaseInvoiceDto> CreatePurchaseInvoiceAsync(PurchaseInvoiceCreateDto createDto, string createdBy)
        {
            var vendor = await _context.Vendors.FindAsync(createDto.VendorId);
            if (vendor == null)
                throw new ArgumentException("Vendor not found");

            var purchaseInvoice = new PurchaseInvoice
            {
                InvoiceNumber = await GenerateInvoiceNumberAsync(),
                VendorId = createDto.VendorId,
                PurchaseOrderId = createDto.PurchaseOrderId,
                GoodsReceiptId = createDto.GoodsReceiptId,
                InvoiceDate = createDto.InvoiceDate,
                DueDate = createDto.DueDate,
                Status = createDto.Status,
                VendorInvoiceNumber = createDto.VendorInvoiceNumber,
                PaymentTerms = createDto.PaymentTerms,
                Notes = createDto.Notes,
                CreatedBy = createdBy,
                CreatedDate = DateTime.UtcNow,
                IsActive = true
            };

            _context.PurchaseInvoices.Add(purchaseInvoice);
            await _context.SaveChangesAsync();

            // Add items and calculate totals
            decimal subTotal = 0;
            decimal totalTaxAmount = 0;
            decimal totalDiscountAmount = 0;

            foreach (var itemDto in createDto.Items)
            {
                var lineTotal = itemDto.Quantity * itemDto.UnitPrice;
                var discountAmount = lineTotal * (itemDto.DiscountRate / 100);
                var taxableAmount = lineTotal - discountAmount;
                var taxAmount = taxableAmount * (itemDto.TaxRate / 100);
                var netAmount = taxableAmount + taxAmount;

                var item = new PurchaseInvoiceItem
                {
                    PurchaseInvoiceId = purchaseInvoice.Id,
                    MaterialId = itemDto.MaterialId,
                    Quantity = itemDto.Quantity,
                    UnitPrice = itemDto.UnitPrice,
                    LineTotal = lineTotal,
                    TaxRate = itemDto.TaxRate,
                    TaxAmount = taxAmount,
                    DiscountRate = itemDto.DiscountRate,
                    DiscountAmount = discountAmount,
                    NetAmount = netAmount,
                    Description = itemDto.Description,
                    CreatedBy = createdBy,
                    CreatedDate = DateTime.UtcNow,
                    IsActive = true
                };

                _context.PurchaseInvoiceItems.Add(item);

                subTotal += lineTotal;
                totalTaxAmount += taxAmount;
                totalDiscountAmount += discountAmount;
            }

            // Update invoice totals
            purchaseInvoice.SubTotal = subTotal;
            purchaseInvoice.TaxAmount = totalTaxAmount;
            purchaseInvoice.DiscountAmount = totalDiscountAmount;
            purchaseInvoice.TotalAmount = subTotal + totalTaxAmount - totalDiscountAmount;
            purchaseInvoice.BalanceAmount = purchaseInvoice.TotalAmount;

            await _context.SaveChangesAsync();

            return await GetPurchaseInvoiceByIdAsync(purchaseInvoice.Id) ?? throw new InvalidOperationException("Failed to retrieve created purchase invoice");
        }

        public async Task<PurchaseInvoiceDto> UpdatePurchaseInvoiceAsync(PurchaseInvoiceUpdateDto updateDto, string modifiedBy)
        {
            var purchaseInvoice = await _context.PurchaseInvoices
                .Include(pi => pi.Items)
                .FirstOrDefaultAsync(pi => pi.Id == updateDto.Id && pi.IsActive);

            if (purchaseInvoice == null)
                throw new ArgumentException("Purchase invoice not found");

            // Update purchase invoice properties
            purchaseInvoice.InvoiceDate = updateDto.InvoiceDate;
            purchaseInvoice.DueDate = updateDto.DueDate;
            purchaseInvoice.Status = updateDto.Status;
            purchaseInvoice.VendorInvoiceNumber = updateDto.VendorInvoiceNumber;
            purchaseInvoice.PaymentTerms = updateDto.PaymentTerms;
            purchaseInvoice.Notes = updateDto.Notes;
            purchaseInvoice.ModifiedBy = modifiedBy;
            purchaseInvoice.ModifiedDate = DateTime.UtcNow;

            // Remove existing items
            _context.PurchaseInvoiceItems.RemoveRange(purchaseInvoice.Items);

            // Add updated items and recalculate totals
            decimal subTotal = 0;
            decimal totalTaxAmount = 0;
            decimal totalDiscountAmount = 0;

            foreach (var itemDto in updateDto.Items)
            {
                var lineTotal = itemDto.Quantity * itemDto.UnitPrice;
                var discountAmount = lineTotal * (itemDto.DiscountRate / 100);
                var taxableAmount = lineTotal - discountAmount;
                var taxAmount = taxableAmount * (itemDto.TaxRate / 100);
                var netAmount = taxableAmount + taxAmount;

                var item = new PurchaseInvoiceItem
                {
                    PurchaseInvoiceId = purchaseInvoice.Id,
                    MaterialId = itemDto.MaterialId,
                    Quantity = itemDto.Quantity,
                    UnitPrice = itemDto.UnitPrice,
                    LineTotal = lineTotal,
                    TaxRate = itemDto.TaxRate,
                    TaxAmount = taxAmount,
                    DiscountRate = itemDto.DiscountRate,
                    DiscountAmount = discountAmount,
                    NetAmount = netAmount,
                    Description = itemDto.Description,
                    CreatedBy = purchaseInvoice.CreatedBy,
                    CreatedDate = purchaseInvoice.CreatedDate,
                    ModifiedBy = modifiedBy,
                    ModifiedDate = DateTime.UtcNow,
                    IsActive = true
                };

                _context.PurchaseInvoiceItems.Add(item);

                subTotal += lineTotal;
                totalTaxAmount += taxAmount;
                totalDiscountAmount += discountAmount;
            }

            // Update invoice totals
            purchaseInvoice.SubTotal = subTotal;
            purchaseInvoice.TaxAmount = totalTaxAmount;
            purchaseInvoice.DiscountAmount = totalDiscountAmount;
            purchaseInvoice.TotalAmount = subTotal + totalTaxAmount - totalDiscountAmount;
            purchaseInvoice.BalanceAmount = purchaseInvoice.TotalAmount - purchaseInvoice.PaidAmount;

            await _context.SaveChangesAsync();

            return await GetPurchaseInvoiceByIdAsync(purchaseInvoice.Id) ?? throw new InvalidOperationException("Failed to retrieve updated purchase invoice");
        }

        public async Task<bool> DeletePurchaseInvoiceAsync(int id)
        {
            var purchaseInvoice = await _context.PurchaseInvoices.FindAsync(id);
            if (purchaseInvoice == null) return false;

            purchaseInvoice.IsActive = false;
            purchaseInvoice.ModifiedDate = DateTime.UtcNow;

            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> SendPurchaseInvoiceAsync(int id, string sentBy)
        {
            var purchaseInvoice = await _context.PurchaseInvoices.FindAsync(id);
            if (purchaseInvoice == null) return false;

            purchaseInvoice.Status = InvoiceStatus.Sent;
            purchaseInvoice.ModifiedBy = sentBy;
            purchaseInvoice.ModifiedDate = DateTime.UtcNow;

            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> PayPurchaseInvoiceAsync(int id, PurchaseInvoicePaymentDto paymentDto, string paidBy)
        {
            var purchaseInvoice = await _context.PurchaseInvoices.FindAsync(id);
            if (purchaseInvoice == null) return false;

            purchaseInvoice.PaidAmount += paymentDto.PaymentAmount;
            purchaseInvoice.BalanceAmount = purchaseInvoice.TotalAmount - purchaseInvoice.PaidAmount;

            if (purchaseInvoice.BalanceAmount <= 0)
            {
                purchaseInvoice.Status = InvoiceStatus.Paid;
                purchaseInvoice.BalanceAmount = 0;
            }
            else if (purchaseInvoice.PaidAmount > 0)
            {
                purchaseInvoice.Status = InvoiceStatus.PartiallyPaid;
            }

            purchaseInvoice.ModifiedBy = paidBy;
            purchaseInvoice.ModifiedDate = DateTime.UtcNow;

            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> MarkAsOverdueAsync(int id)
        {
            var purchaseInvoice = await _context.PurchaseInvoices.FindAsync(id);
            if (purchaseInvoice == null) return false;

            if (purchaseInvoice.DueDate < DateTime.UtcNow && purchaseInvoice.BalanceAmount > 0)
            {
                purchaseInvoice.Status = InvoiceStatus.Overdue;
                purchaseInvoice.ModifiedDate = DateTime.UtcNow;
                await _context.SaveChangesAsync();
                return true;
            }

            return false;
        }

        public async Task<IEnumerable<PurchaseInvoiceListDto>> GetPurchaseInvoicesByVendorAsync(int vendorId)
        {
            return await _context.PurchaseInvoices
                .Include(pi => pi.Vendor)
                .Include(pi => pi.PurchaseOrder)
                .Include(pi => pi.GoodsReceipt)
                .Include(pi => pi.Items)
                .Where(pi => pi.VendorId == vendorId && pi.IsActive)
                .Select(pi => new PurchaseInvoiceListDto
                {
                    Id = pi.Id,
                    InvoiceNumber = pi.InvoiceNumber,
                    VendorName = pi.Vendor.Name,
                    VendorInvoiceNumber = pi.VendorInvoiceNumber,
                    InvoiceDate = pi.InvoiceDate,
                    DueDate = pi.DueDate,
                    Status = pi.Status.ToString(),
                    PaymentTerms = pi.PaymentTerms.ToString(),
                    TotalAmount = pi.TotalAmount,
                    PaidAmount = pi.PaidAmount,
                    BalanceAmount = pi.BalanceAmount,
                    PurchaseOrderNumber = pi.PurchaseOrder != null ? pi.PurchaseOrder.OrderNumber : "",
                    GoodsReceiptNumber = pi.GoodsReceipt != null ? pi.GoodsReceipt.ReceiptNumber : "",
                    IsActive = pi.IsActive,
                    CreatedDate = pi.CreatedDate,
                    ItemCount = pi.Items.Count,
                    DaysOverdue = pi.Status == InvoiceStatus.Overdue ? (int)(DateTime.UtcNow - pi.DueDate).TotalDays : 0
                })
                .OrderByDescending(pi => pi.CreatedDate)
                .ToListAsync();
        }

        // Additional methods will be added in the next part due to length constraints
        public async Task<string> GenerateInvoiceNumberAsync()
        {
            var lastInvoice = await _context.PurchaseInvoices
                .OrderByDescending(pi => pi.Id)
                .FirstOrDefaultAsync();

            var nextNumber = (lastInvoice?.Id ?? 0) + 1;
            return $"PI{DateTime.Now:yyyyMM}{nextNumber:D4}";
        }

        public async Task<bool> CanDeletePurchaseInvoiceAsync(int id)
        {
            var purchaseInvoice = await _context.PurchaseInvoices.FindAsync(id);
            return purchaseInvoice?.Status == InvoiceStatus.Draft;
        }

        public async Task<bool> CanEditPurchaseInvoiceAsync(int id)
        {
            var purchaseInvoice = await _context.PurchaseInvoices.FindAsync(id);
            return purchaseInvoice?.Status == InvoiceStatus.Draft;
        }

        public async Task<bool> PurchaseInvoiceExistsAsync(int id)
        {
            return await _context.PurchaseInvoices.AnyAsync(pi => pi.Id == id && pi.IsActive);
        }

        public async Task<IEnumerable<PurchaseInvoiceListDto>> GetPurchaseInvoicesByStatusAsync(InvoiceStatus status)
        {
            return await _context.PurchaseInvoices
                .Include(pi => pi.Vendor)
                .Include(pi => pi.PurchaseOrder)
                .Include(pi => pi.GoodsReceipt)
                .Include(pi => pi.Items)
                .Where(pi => pi.Status == status && pi.IsActive)
                .Select(pi => new PurchaseInvoiceListDto
                {
                    Id = pi.Id,
                    InvoiceNumber = pi.InvoiceNumber,
                    VendorName = pi.Vendor.Name,
                    VendorInvoiceNumber = pi.VendorInvoiceNumber,
                    InvoiceDate = pi.InvoiceDate,
                    DueDate = pi.DueDate,
                    Status = pi.Status.ToString(),
                    PaymentTerms = pi.PaymentTerms.ToString(),
                    TotalAmount = pi.TotalAmount,
                    PaidAmount = pi.PaidAmount,
                    BalanceAmount = pi.BalanceAmount,
                    PurchaseOrderNumber = pi.PurchaseOrder != null ? pi.PurchaseOrder.OrderNumber : "",
                    GoodsReceiptNumber = pi.GoodsReceipt != null ? pi.GoodsReceipt.ReceiptNumber : "",
                    IsActive = pi.IsActive,
                    CreatedDate = pi.CreatedDate,
                    ItemCount = pi.Items.Count,
                    DaysOverdue = pi.Status == InvoiceStatus.Overdue ? (int)(DateTime.UtcNow - pi.DueDate).TotalDays : 0
                })
                .OrderByDescending(pi => pi.CreatedDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<PurchaseInvoiceListDto>> GetOverduePurchaseInvoicesAsync()
        {
            return await GetPurchaseInvoicesByStatusAsync(InvoiceStatus.Overdue);
        }

        public async Task<IEnumerable<PurchaseInvoiceListDto>> SearchPurchaseInvoicesAsync(string searchTerm)
        {
            return await _context.PurchaseInvoices
                .Include(pi => pi.Vendor)
                .Include(pi => pi.PurchaseOrder)
                .Include(pi => pi.GoodsReceipt)
                .Include(pi => pi.Items)
                .Where(pi => pi.IsActive && (
                    pi.InvoiceNumber.Contains(searchTerm) ||
                    pi.VendorInvoiceNumber.Contains(searchTerm) ||
                    pi.Vendor.Name.Contains(searchTerm)))
                .Select(pi => new PurchaseInvoiceListDto
                {
                    Id = pi.Id,
                    InvoiceNumber = pi.InvoiceNumber,
                    VendorName = pi.Vendor.Name,
                    VendorInvoiceNumber = pi.VendorInvoiceNumber,
                    InvoiceDate = pi.InvoiceDate,
                    DueDate = pi.DueDate,
                    Status = pi.Status.ToString(),
                    PaymentTerms = pi.PaymentTerms.ToString(),
                    TotalAmount = pi.TotalAmount,
                    PaidAmount = pi.PaidAmount,
                    BalanceAmount = pi.BalanceAmount,
                    PurchaseOrderNumber = pi.PurchaseOrder != null ? pi.PurchaseOrder.OrderNumber : "",
                    GoodsReceiptNumber = pi.GoodsReceipt != null ? pi.GoodsReceipt.ReceiptNumber : "",
                    IsActive = pi.IsActive,
                    CreatedDate = pi.CreatedDate,
                    ItemCount = pi.Items.Count,
                    DaysOverdue = pi.Status == InvoiceStatus.Overdue ? (int)(DateTime.UtcNow - pi.DueDate).TotalDays : 0
                })
                .OrderByDescending(pi => pi.CreatedDate)
                .ToListAsync();
        }

        public async Task<PurchaseInvoiceDto?> GetPurchaseInvoiceByGoodsReceiptAsync(int goodsReceiptId)
        {
            var purchaseInvoice = await _context.PurchaseInvoices
                .Include(pi => pi.Vendor)
                .Include(pi => pi.PurchaseOrder)
                .Include(pi => pi.GoodsReceipt)
                .Include(pi => pi.Items)
                    .ThenInclude(item => item.Material)
                .FirstOrDefaultAsync(pi => pi.GoodsReceiptId == goodsReceiptId && pi.IsActive);

            if (purchaseInvoice == null) return null;

            return await GetPurchaseInvoiceByIdAsync(purchaseInvoice.Id);
        }

        public async Task<PurchaseInvoiceDto?> GetPurchaseInvoiceByPurchaseOrderAsync(int purchaseOrderId)
        {
            var purchaseInvoice = await _context.PurchaseInvoices
                .Include(pi => pi.Vendor)
                .Include(pi => pi.PurchaseOrder)
                .Include(pi => pi.GoodsReceipt)
                .Include(pi => pi.Items)
                    .ThenInclude(item => item.Material)
                .FirstOrDefaultAsync(pi => pi.PurchaseOrderId == purchaseOrderId && pi.IsActive);

            if (purchaseInvoice == null) return null;

            return await GetPurchaseInvoiceByIdAsync(purchaseInvoice.Id);
        }

        public async Task<PurchaseInvoiceSummaryDto> GetPurchaseInvoiceSummaryAsync()
        {
            var totalInvoices = await _context.PurchaseInvoices.CountAsync(pi => pi.IsActive);
            var draftInvoices = await _context.PurchaseInvoices.CountAsync(pi => pi.Status == InvoiceStatus.Draft && pi.IsActive);
            var sentInvoices = await _context.PurchaseInvoices.CountAsync(pi => pi.Status == InvoiceStatus.Sent && pi.IsActive);
            var paidInvoices = await _context.PurchaseInvoices.CountAsync(pi => pi.Status == InvoiceStatus.Paid && pi.IsActive);
            var overdueInvoices = await _context.PurchaseInvoices.CountAsync(pi => pi.Status == InvoiceStatus.Overdue && pi.IsActive);

            var totalAmount = await _context.PurchaseInvoices.Where(pi => pi.IsActive).SumAsync(pi => pi.TotalAmount);
            var paidAmount = await _context.PurchaseInvoices.Where(pi => pi.IsActive).SumAsync(pi => pi.PaidAmount);
            var outstandingAmount = await _context.PurchaseInvoices.Where(pi => pi.IsActive).SumAsync(pi => pi.BalanceAmount);
            var overdueAmount = await _context.PurchaseInvoices.Where(pi => pi.Status == InvoiceStatus.Overdue && pi.IsActive).SumAsync(pi => pi.BalanceAmount);

            return new PurchaseInvoiceSummaryDto
            {
                TotalInvoices = totalInvoices,
                DraftInvoices = draftInvoices,
                SentInvoices = sentInvoices,
                PaidInvoices = paidInvoices,
                OverdueInvoices = overdueInvoices,
                TotalAmount = totalAmount,
                PaidAmount = paidAmount,
                OutstandingAmount = outstandingAmount,
                OverdueAmount = overdueAmount
            };
        }

        public async Task<IEnumerable<PurchaseInvoiceListDto>> GetRecentPurchaseInvoicesAsync(int count = 10)
        {
            return await _context.PurchaseInvoices
                .Include(pi => pi.Vendor)
                .Include(pi => pi.PurchaseOrder)
                .Include(pi => pi.GoodsReceipt)
                .Include(pi => pi.Items)
                .Where(pi => pi.IsActive)
                .OrderByDescending(pi => pi.CreatedDate)
                .Take(count)
                .Select(pi => new PurchaseInvoiceListDto
                {
                    Id = pi.Id,
                    InvoiceNumber = pi.InvoiceNumber,
                    VendorName = pi.Vendor.Name,
                    VendorInvoiceNumber = pi.VendorInvoiceNumber,
                    InvoiceDate = pi.InvoiceDate,
                    DueDate = pi.DueDate,
                    Status = pi.Status.ToString(),
                    PaymentTerms = pi.PaymentTerms.ToString(),
                    TotalAmount = pi.TotalAmount,
                    PaidAmount = pi.PaidAmount,
                    BalanceAmount = pi.BalanceAmount,
                    PurchaseOrderNumber = pi.PurchaseOrder != null ? pi.PurchaseOrder.OrderNumber : "",
                    GoodsReceiptNumber = pi.GoodsReceipt != null ? pi.GoodsReceipt.ReceiptNumber : "",
                    IsActive = pi.IsActive,
                    CreatedDate = pi.CreatedDate,
                    ItemCount = pi.Items.Count,
                    DaysOverdue = pi.Status == InvoiceStatus.Overdue ? (int)(DateTime.UtcNow - pi.DueDate).TotalDays : 0
                })
                .ToListAsync();
        }

        public async Task<IEnumerable<PurchaseInvoiceListDto>> GetPurchaseInvoicesDueInDaysAsync(int days)
        {
            var dueDate = DateTime.UtcNow.AddDays(days);
            return await _context.PurchaseInvoices
                .Include(pi => pi.Vendor)
                .Include(pi => pi.PurchaseOrder)
                .Include(pi => pi.GoodsReceipt)
                .Include(pi => pi.Items)
                .Where(pi => pi.IsActive && pi.DueDate <= dueDate && pi.BalanceAmount > 0)
                .Select(pi => new PurchaseInvoiceListDto
                {
                    Id = pi.Id,
                    InvoiceNumber = pi.InvoiceNumber,
                    VendorName = pi.Vendor.Name,
                    VendorInvoiceNumber = pi.VendorInvoiceNumber,
                    InvoiceDate = pi.InvoiceDate,
                    DueDate = pi.DueDate,
                    Status = pi.Status.ToString(),
                    PaymentTerms = pi.PaymentTerms.ToString(),
                    TotalAmount = pi.TotalAmount,
                    PaidAmount = pi.PaidAmount,
                    BalanceAmount = pi.BalanceAmount,
                    PurchaseOrderNumber = pi.PurchaseOrder != null ? pi.PurchaseOrder.OrderNumber : "",
                    GoodsReceiptNumber = pi.GoodsReceipt != null ? pi.GoodsReceipt.ReceiptNumber : "",
                    IsActive = pi.IsActive,
                    CreatedDate = pi.CreatedDate,
                    ItemCount = pi.Items.Count,
                    DaysOverdue = pi.Status == InvoiceStatus.Overdue ? (int)(DateTime.UtcNow - pi.DueDate).TotalDays : 0
                })
                .OrderBy(pi => pi.DueDate)
                .ToListAsync();
        }

        public async Task<decimal> GetTotalOutstandingAmountAsync()
        {
            return await _context.PurchaseInvoices
                .Where(pi => pi.IsActive && pi.BalanceAmount > 0)
                .SumAsync(pi => pi.BalanceAmount);
        }

        public async Task<decimal> GetTotalOutstandingAmountByVendorAsync(int vendorId)
        {
            return await _context.PurchaseInvoices
                .Where(pi => pi.VendorId == vendorId && pi.IsActive && pi.BalanceAmount > 0)
                .SumAsync(pi => pi.BalanceAmount);
        }
    }
}
