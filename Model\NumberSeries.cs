using System.ComponentModel.DataAnnotations;

namespace JayERP.Model
{
    public class NumberSeries : BaseEntity
    {
        [Required]
        [MaxLength(50)]
        public string SeriesName { get; set; } = string.Empty;
        
        public DocumentType DocumentType { get; set; }
        
        [Required]
        [MaxLength(20)]
        public string Prefix { get; set; } = string.Empty;
        
        [MaxLength(20)]
        public string Suffix { get; set; } = string.Empty;
        
        [Range(1, int.MaxValue)]
        public int StartingNumber { get; set; } = 1;
        
        [Range(1, int.MaxValue)]
        public int CurrentNumber { get; set; } = 1;
        
        [Range(1, int.MaxValue)]
        public int IncrementBy { get; set; } = 1;
        
        [Range(1, 10)]
        public int PaddingLength { get; set; } = 4;
        
        [MaxLength(20)]
        public string DateFormat { get; set; } = string.Empty; // YYYY, MM, DD
        
        [MaxLength(100)]
        public string Format { get; set; } = string.Empty; // e.g., "PO-{YYYY}-{0000}"
        
        public bool IsDefault { get; set; } = false;
        
        [MaxLength(500)]
        public string Description { get; set; } = string.Empty;
        
        // Method to generate next number
        public string GenerateNextNumber()
        {
            var number = CurrentNumber.ToString().PadLeft(PaddingLength, '0');
            var result = Format
                .Replace("{YYYY}", DateTime.Now.Year.ToString())
                .Replace("{MM}", DateTime.Now.Month.ToString("00"))
                .Replace("{DD}", DateTime.Now.Day.ToString("00"))
                .Replace("{0000}", number);
            
            CurrentNumber += IncrementBy;
            return result;
        }
    }
}
