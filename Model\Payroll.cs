using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace JayERP.Model
{
    public class Payroll : BaseEntity
    {
        [Required]
        public int EmployeeId { get; set; }
        
        [Required]
        [MaxLength(50)]
        public string PayrollNumber { get; set; } = string.Empty;
        
        public DateTime PayrollMonth { get; set; }
        
        [Range(0, 31)]
        public int WorkingDays { get; set; }
        
        [Range(0, 31)]
        public int PresentDays { get; set; }
        
        [Range(0, 31)]
        public int AbsentDays { get; set; }
        
        [Range(0, 31)]
        public int LeaveDays { get; set; }
        
        [Range(0, 31)]
        public int HolidayDays { get; set; }
        
        [Range(0, double.MaxValue)]
        public decimal OvertimeHours { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal BasicSalary { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal HRA { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal ConveyanceAllowance { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal MedicalAllowance { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal OtherAllowances { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal OvertimeAmount { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal Bonus { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal Incentives { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal GrossEarnings { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal PFDeduction { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal ESIDeduction { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal TaxDeduction { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal ProfessionalTax { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal LoanDeduction { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal OtherDeductions { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalDeductions { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal NetSalary { get; set; }
        
        public DateTime? ProcessedDate { get; set; }
        
        [MaxLength(100)]
        public string ProcessedBy { get; set; } = string.Empty;
        
        public bool IsPaid { get; set; } = false;
        
        public DateTime? PaidDate { get; set; }
        
        [MaxLength(1000)]
        public string Notes { get; set; } = string.Empty;
        
        // Navigation Properties
        [ForeignKey("EmployeeId")]
        public virtual Employee Employee { get; set; } = null!;
    }
}
