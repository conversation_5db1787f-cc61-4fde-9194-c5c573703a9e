using System.ComponentModel.DataAnnotations;
using JayERP.Model;

namespace JayERP.DTOs
{
    public class PurchaseOrderDto
    {
        public int Id { get; set; }
        public string OrderNumber { get; set; } = string.Empty;
        public int VendorId { get; set; }
        public string VendorName { get; set; } = string.Empty;
        public DateTime OrderDate { get; set; }
        public DateTime? ExpectedDeliveryDate { get; set; }
        public PurchaseOrderStatus Status { get; set; }
        public PaymentTerms PaymentTerms { get; set; }
        public decimal SubTotal { get; set; }
        public decimal TaxAmount { get; set; }
        public decimal DiscountAmount { get; set; }
        public decimal TotalAmount { get; set; }
        public string ApprovedBy { get; set; } = string.Empty;
        public DateTime? ApprovedDate { get; set; }
        public string Notes { get; set; } = string.Empty;
        public string DeliveryAddress { get; set; } = string.Empty;
        public List<PurchaseOrderItemDto> Items { get; set; } = new();
        public DateTime CreatedDate { get; set; }
        public string CreatedBy { get; set; } = string.Empty;
    }

    public class PurchaseOrderCreateDto
    {
        [Required(ErrorMessage = "Vendor is required")]
        public int VendorId { get; set; }
        
        [Required(ErrorMessage = "Order date is required")]
        public DateTime OrderDate { get; set; } = DateTime.Today;
        
        public DateTime? ExpectedDeliveryDate { get; set; }
        
        [Required(ErrorMessage = "Payment terms are required")]
        public PaymentTerms PaymentTerms { get; set; }
        
        [StringLength(1000, ErrorMessage = "Notes cannot exceed 1000 characters")]
        public string Notes { get; set; } = string.Empty;
        
        [Required(ErrorMessage = "Delivery address is required")]
        [StringLength(500, ErrorMessage = "Delivery address cannot exceed 500 characters")]
        public string DeliveryAddress { get; set; } = string.Empty;
        
        [Required(ErrorMessage = "At least one item is required")]
        [MinLength(1, ErrorMessage = "At least one item is required")]
        public List<PurchaseOrderItemCreateDto> Items { get; set; } = new();
    }

    public class PurchaseOrderUpdateDto : PurchaseOrderCreateDto
    {
        public int Id { get; set; }
        public PurchaseOrderStatus Status { get; set; }
    }

    public class PurchaseOrderListDto
    {
        public int Id { get; set; }
        public string OrderNumber { get; set; } = string.Empty;
        public string VendorName { get; set; } = string.Empty;
        public DateTime OrderDate { get; set; }
        public DateTime? ExpectedDeliveryDate { get; set; }
        public string Status { get; set; } = string.Empty;
        public decimal TotalAmount { get; set; }
        public string ApprovedBy { get; set; } = string.Empty;
        public DateTime? ApprovedDate { get; set; }
        public DateTime CreatedDate { get; set; }
        public int ItemCount { get; set; }
    }

    public class PurchaseOrderItemDto
    {
        public int Id { get; set; }
        public int PurchaseOrderId { get; set; }
        public int MaterialId { get; set; }
        public string MaterialName { get; set; } = string.Empty;
        public string MaterialCode { get; set; } = string.Empty;
        public decimal Quantity { get; set; }
        public decimal UnitPrice { get; set; }
        public decimal LineTotal { get; set; }
        public decimal ReceivedQuantity { get; set; }
        public decimal PendingQuantity { get; set; }
        public DateTime? RequiredDate { get; set; }
        public string Notes { get; set; } = string.Empty;
    }

    public class PurchaseOrderItemCreateDto
    {
        [Required(ErrorMessage = "Material is required")]
        public int MaterialId { get; set; }
        
        [Required(ErrorMessage = "Quantity is required")]
        [Range(0.01, double.MaxValue, ErrorMessage = "Quantity must be greater than 0")]
        public decimal Quantity { get; set; }
        
        [Required(ErrorMessage = "Unit price is required")]
        [Range(0.01, double.MaxValue, ErrorMessage = "Unit price must be greater than 0")]
        public decimal UnitPrice { get; set; }
        
        public DateTime? RequiredDate { get; set; }
        
        [StringLength(500, ErrorMessage = "Notes cannot exceed 500 characters")]
        public string Notes { get; set; } = string.Empty;
    }

    public class PurchaseOrderApprovalDto
    {
        public int Id { get; set; }
        public bool IsApproved { get; set; }
        public string ApprovalComments { get; set; } = string.Empty;
    }
}
