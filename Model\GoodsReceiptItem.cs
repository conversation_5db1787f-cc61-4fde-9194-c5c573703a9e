using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace JayERP.Model
{
    public class GoodsReceiptItem : BaseEntity
    {
        [Required]
        public int GoodsReceiptId { get; set; }
        
        [Required]
        public int PurchaseOrderItemId { get; set; }
        
        [Required]
        public int MaterialId { get; set; }
        
        [Range(0, double.MaxValue)]
        public decimal OrderedQuantity { get; set; }
        
        [Range(0, double.MaxValue)]
        public decimal ReceivedQuantity { get; set; }
        
        [Range(0, double.MaxValue)]
        public decimal AcceptedQuantity { get; set; }
        
        [Range(0, double.MaxValue)]
        public decimal RejectedQuantity { get; set; }
        
        [Range(0, double.MaxValue)]
        public decimal UnitPrice { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal LineTotal { get; set; }
        
        [MaxLength(50)]
        public string BatchNumber { get; set; } = string.Empty;
        
        public DateTime? ExpiryDate { get; set; }
        
        [MaxLength(100)]
        public string StorageLocation { get; set; } = string.Empty;
        
        [MaxLength(500)]
        public string QualityNotes { get; set; } = string.Empty;
        
        [MaxLength(500)]
        public string RejectionReason { get; set; } = string.Empty;
        
        // Navigation Properties
        [ForeignKey("GoodsReceiptId")]
        public virtual GoodsReceipt GoodsReceipt { get; set; } = null!;
        
        [ForeignKey("PurchaseOrderItemId")]
        public virtual PurchaseOrderItem PurchaseOrderItem { get; set; } = null!;
        
        [ForeignKey("MaterialId")]
        public virtual Material Material { get; set; } = null!;
    }
}
