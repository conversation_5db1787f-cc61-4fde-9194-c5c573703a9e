﻿using System.ComponentModel.DataAnnotations;

namespace JayERP.Model
{
    public class Vendor : BaseEntity
    {
        [Required]
        [MaxLength(100)]
        public string Name { get; set; } = string.Empty;

        [Required]
        [MaxLength(200)]
        public string CompanyName { get; set; } = string.Empty;

        [Required]
        [MaxLength(500)]
        public string Address { get; set; } = string.Empty;

        [MaxLength(100)]
        public string City { get; set; } = string.Empty;

        [MaxLength(50)]
        public string State { get; set; } = string.Empty;

        [MaxLength(20)]
        public string PostalCode { get; set; } = string.Empty;

        [MaxLength(50)]
        public string Country { get; set; } = string.Empty;

        [Required]
        [EmailAddress]
        [MaxLength(100)]
        public string Email { get; set; } = string.Empty;

        [MaxLength(20)]
        public string Phone { get; set; } = string.Empty;

        [MaxLength(20)]
        public string Mobile { get; set; } = string.Empty;

        [MaxLength(100)]
        public string ContactPerson { get; set; } = string.Empty;

        [MaxLength(50)]
        public string TaxNumber { get; set; } = string.Empty;

        [MaxLength(50)]
        public string RegistrationNumber { get; set; } = string.Empty;

        public VendorCategory Category { get; set; }

        public PaymentTerms PaymentTerms { get; set; }

        [Range(0, double.MaxValue)]
        public decimal CreditLimit { get; set; }

        [Range(0, 5)]
        public int QualityRating { get; set; } = 3;

        [Range(0, 5)]
        public int DeliveryRating { get; set; } = 3;

        [MaxLength(1000)]
        public string Notes { get; set; } = string.Empty;

        // Navigation Properties
        public virtual ICollection<PurchaseOrder> PurchaseOrders { get; set; } = new List<PurchaseOrder>();
        public virtual ICollection<PurchaseInvoice> PurchaseInvoices { get; set; } = new List<PurchaseInvoice>();
    }
}
