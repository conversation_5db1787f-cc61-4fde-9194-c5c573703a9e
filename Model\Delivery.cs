using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace JayERP.Model
{
    public class Delivery : BaseEntity
    {
        [Required]
        [MaxLength(50)]
        public string DeliveryNumber { get; set; } = string.Empty;
        
        [Required]
        public int SalesOrderId { get; set; }
        
        [Required]
        public int CustomerId { get; set; }
        
        public DateTime DeliveryDate { get; set; } = DateTime.UtcNow;
        
        public DateTime? ScheduledDate { get; set; }
        
        [MaxLength(500)]
        public string DeliveryAddress { get; set; } = string.Empty;
        
        [MaxLength(100)]
        public string DeliveredBy { get; set; } = string.Empty;
        
        [MaxLength(100)]
        public string ReceivedBy { get; set; } = string.Empty;
        
        [MaxLength(50)]
        public string VehicleNumber { get; set; } = string.Empty;
        
        [MaxLength(100)]
        public string DriverName { get; set; } = string.Empty;
        
        [MaxLength(20)]
        public string DriverContact { get; set; } = string.Empty;
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalValue { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal FreightCharges { get; set; }
        
        [MaxLength(1000)]
        public string DeliveryInstructions { get; set; } = string.Empty;
        
        [MaxLength(1000)]
        public string Notes { get; set; } = string.Empty;
        
        public bool IsCompleted { get; set; } = false;
        
        // Navigation Properties
        [ForeignKey("SalesOrderId")]
        public virtual SalesOrder SalesOrder { get; set; } = null!;
        
        [ForeignKey("CustomerId")]
        public virtual Customer Customer { get; set; } = null!;
        
        public virtual ICollection<DeliveryItem> Items { get; set; } = new List<DeliveryItem>();
    }
}
