using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace JayERP.Model
{
    public class PurchaseOrderItem : BaseEntity
    {
        [Required]
        public int PurchaseOrderId { get; set; }
        
        [Required]
        public int MaterialId { get; set; }
        
        [Range(0, double.MaxValue)]
        public decimal Quantity { get; set; }
        
        [Range(0, double.MaxValue)]
        public decimal UnitPrice { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal LineTotal { get; set; }
        
        [Range(0, double.MaxValue)]
        public decimal ReceivedQuantity { get; set; }
        
        [Range(0, double.MaxValue)]
        public decimal PendingQuantity { get; set; }
        
        public DateTime? RequiredDate { get; set; }
        
        [MaxLength(500)]
        public string Notes { get; set; } = string.Empty;
        
        // Navigation Properties
        [ForeignKey("PurchaseOrderId")]
        public virtual PurchaseOrder PurchaseOrder { get; set; } = null!;
        
        [ForeignKey("MaterialId")]
        public virtual Material Material { get; set; } = null!;
        
        public virtual ICollection<GoodsReceiptItem> GoodsReceiptItems { get; set; } = new List<GoodsReceiptItem>();
    }
}
