using JayERP.DTOs;
using JayERP.Model;

namespace JayERP.Services
{
    public interface IVendorService
    {
        Task<IEnumerable<VendorListDto>> GetAllVendorsAsync();
        Task<VendorDto?> GetVendorByIdAsync(int id);
        Task<VendorDto> CreateVendorAsync(VendorCreateDto vendorCreateDto, string createdBy);
        Task<VendorDto> UpdateVendorAsync(VendorUpdateDto vendorUpdateDto, string modifiedBy);
        Task<bool> DeleteVendorAsync(int id);
        Task<bool> VendorExistsAsync(int id);
        Task<bool> IsEmailUniqueAsync(string email, int? excludeId = null);
        Task<IEnumerable<VendorListDto>> SearchVendorsAsync(string searchTerm);
        Task<IEnumerable<VendorListDto>> GetVendorsByCategoryAsync(VendorCategory category);
        Task<VendorPerformanceDto?> GetVendorPerformanceAsync(int vendorId);
        Task<IEnumerable<VendorPerformanceDto>> GetTopVendorsByPerformanceAsync(int count = 10);
        Task UpdateVendorRatingAsync(int vendorId, int qualityRating, int deliveryRating);
    }
}
