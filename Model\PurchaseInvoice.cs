using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace JayERP.Model
{
    public class PurchaseInvoice : BaseEntity
    {
        [Required]
        [MaxLength(50)]
        public string InvoiceNumber { get; set; } = string.Empty;
        
        [Required]
        public int VendorId { get; set; }
        
        public int? PurchaseOrderId { get; set; }
        
        public int? GoodsReceiptId { get; set; }
        
        public DateTime InvoiceDate { get; set; } = DateTime.UtcNow;
        
        public DateTime DueDate { get; set; }
        
        public InvoiceStatus Status { get; set; } = InvoiceStatus.Draft;
        
        [MaxLength(50)]
        public string VendorInvoiceNumber { get; set; } = string.Empty;
        
        public PaymentTerms PaymentTerms { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal SubTotal { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal TaxAmount { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal DiscountAmount { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalAmount { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal PaidAmount { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal BalanceAmount { get; set; }
        
        [MaxLength(1000)]
        public string Notes { get; set; } = string.Empty;
        
        // Navigation Properties
        [ForeignKey("VendorId")]
        public virtual Vendor Vendor { get; set; } = null!;
        
        [ForeignKey("PurchaseOrderId")]
        public virtual PurchaseOrder? PurchaseOrder { get; set; }
        
        [ForeignKey("GoodsReceiptId")]
        public virtual GoodsReceipt? GoodsReceipt { get; set; }
        
        public virtual ICollection<PurchaseInvoiceItem> Items { get; set; } = new List<PurchaseInvoiceItem>();
        public virtual ICollection<PaymentTransaction> PaymentTransactions { get; set; } = new List<PaymentTransaction>();
    }
}
