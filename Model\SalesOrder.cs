using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace JayERP.Model
{
    public class SalesOrder : BaseEntity
    {
        [Required]
        [MaxLength(50)]
        public string OrderNumber { get; set; } = string.Empty;
        
        [Required]
        public int CustomerId { get; set; }
        
        public DateTime OrderDate { get; set; } = DateTime.UtcNow;
        
        public DateTime? RequiredDate { get; set; }
        
        public DateTime? PromisedDate { get; set; }
        
        public SalesOrderStatus Status { get; set; } = SalesOrderStatus.Draft;
        
        public Priority Priority { get; set; } = Priority.Medium;
        
        public PaymentTerms PaymentTerms { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal SubTotal { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal TaxAmount { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal DiscountAmount { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal ShippingCharges { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalAmount { get; set; }
        
        [MaxLength(500)]
        public string ShippingAddress { get; set; } = string.Empty;
        
        [MaxLength(100)]
        public string SalesRepresentative { get; set; } = string.Empty;
        
        [MaxLength(50)]
        public string CustomerPO { get; set; } = string.Empty;
        
        [MaxLength(1000)]
        public string SpecialInstructions { get; set; } = string.Empty;
        
        [MaxLength(1000)]
        public string Notes { get; set; } = string.Empty;
        
        // Navigation Properties
        [ForeignKey("CustomerId")]
        public virtual Customer Customer { get; set; } = null!;
        
        public virtual ICollection<SalesOrderItem> Items { get; set; } = new List<SalesOrderItem>();
        public virtual ICollection<ProductionOrder> ProductionOrders { get; set; } = new List<ProductionOrder>();
        public virtual ICollection<Delivery> Deliveries { get; set; } = new List<Delivery>();
    }
}
