@rendermode InteractiveServer
@using Microsoft.FluentUI.AspNetCore.Components.Icons.Regular
@using Icons = Microsoft.FluentUI.AspNetCore.Components.Icons
@implements IDisposable

@inject NavigationManager NavigationManager

<div class="navmenu">
    <input type="checkbox" title="Menu expand/collapse toggle" id="navmenu-toggle" class="navmenu-icon"/>
    <label for="navmenu-toggle" class="navmenu-icon">
        <FluentIcon Value="@(new Size20.Navigation())" Color="Color.Fill"/>
    </label>
    <nav class="sitenav" aria-labelledby="main-menu">
        <FluentNavMenu Id="main-menu" Collapsible="true" Width="250" Title="Navigation menu" @bind-Expanded="expanded" CustomToggle="true">
            <FluentNavLink Href="/" Match="NavLinkMatch.All" Icon="@(new Size20.Home())" IconColor="Color.Accent">Home</FluentNavLink>

            <FluentNavGroup Text="Procurement" Icon="@(new Size20.Storage())" IconColor="Color.Accent" Title="Procurement">
                <FluentNavLink Href="vendors" Icon="@(new Size20.People())" IconColor="Color.Accent">Vendors</FluentNavLink>
                <FluentNavLink Href="purchase-orders" Icon="@(new Size20.DocumentText())" IconColor="Color.Accent">Purchase Orders</FluentNavLink>
                <FluentNavLink Href="goods-receipt" Icon="@(new Size20.BoxCheckmark())" IconColor="Color.Accent">Goods Receipt</FluentNavLink>
                <FluentNavLink Href="purchase-invoices" Icon="@(new Size20.Receipt())" IconColor="Color.Accent">Purchase Invoices</FluentNavLink>
            </FluentNavGroup>

            <FluentNavGroup Text="Inventory" Icon="@(new Size20.Archive())" IconColor="Color.Accent" Title="Inventory">
                <FluentNavLink Href="materials" Icon="@(new Size20.Cube())" IconColor="Color.Accent">Materials</FluentNavLink>
                <FluentNavLink Href="stock-movements" Icon="@(new Size20.ArrowSwap())" IconColor="Color.Accent">Stock Movements</FluentNavLink>
                <FluentNavLink Href="inventory-balance" Icon="@(new Size20.ChartMultiple())" IconColor="Color.Accent">Inventory Balance</FluentNavLink>
            </FluentNavGroup>

            <FluentNavGroup Text="Production" Icon="@(new Size20.Settings())" IconColor="Color.Accent" Title="Production">
                <FluentNavLink Href="products" Icon="@(new Size20.Food())" IconColor="Color.Accent">Products</FluentNavLink>
                <FluentNavLink Href="bom" Icon="@(new Size20.DocumentBulletList())" IconColor="Color.Accent">Bill of Materials</FluentNavLink>
                <FluentNavLink Href="production-orders" Icon="@(new Size20.Production())" IconColor="Color.Accent">Production Orders</FluentNavLink>
            </FluentNavGroup>

            <FluentNavGroup Text="Sales" Icon="@(new Size20.MoneyHand())" IconColor="Color.Accent" Title="Sales">
                <FluentNavLink Href="customers" Icon="@(new Size20.PersonAccounts())" IconColor="Color.Accent">Customers</FluentNavLink>
                <FluentNavLink Href="sales-orders" Icon="@(new Size20.ShoppingBag())" IconColor="Color.Accent">Sales Orders</FluentNavLink>
                <FluentNavLink Href="deliveries" Icon="@(new Size20.Cart())" IconColor="Color.Accent">Deliveries</FluentNavLink>
                <FluentNavLink Href="sales-invoices" Icon="@(new Size20.ReceiptMoney())" IconColor="Color.Accent">Sales Invoices</FluentNavLink>
            </FluentNavGroup>

            <FluentNavLink Href="counter" Icon="@(new Size20.NumberSymbolSquare())" IconColor="Color.Accent">Counter</FluentNavLink>
            <FluentNavLink Href="weather" Icon="@(new Size20.WeatherPartlyCloudyDay())" IconColor="Color.Accent">Weather</FluentNavLink>
            <FluentNavLink Href="auth" Icon="@(new Size20.LockClosedKey())" IconColor="Color.Accent">Auth Required</FluentNavLink>
            <AuthorizeView>
                <Authorized>
                    <FluentNavLink Href="Account/Manage">@context.User.Identity?.Name</FluentNavLink>
                    <form action="Account/Logout" method="post">
                        <AntiforgeryToken/>
                        <input type="hidden" name="ReturnUrl" value="@currentUrl"/>
                        <FluentButton Type="ButtonType.Submit" Style="width: 100%;">Logout</FluentButton>
                    </form>
                </Authorized>
                <NotAuthorized>
                    <FluentNavLink Href="Account/Register">Register</FluentNavLink>
                    <FluentNavLink Href="Account/Login">Login</FluentNavLink>
                </NotAuthorized>
            </AuthorizeView>
        </FluentNavMenu>
    </nav>
</div>

@code {
    private bool expanded = true;
    private string? currentUrl;

    protected override void OnInitialized()
    {
        currentUrl = NavigationManager.ToBaseRelativePath(NavigationManager.Uri);
        NavigationManager.LocationChanged += OnLocationChanged;
    }

    private void OnLocationChanged(object? sender, LocationChangedEventArgs e)
    {
        currentUrl = NavigationManager.ToBaseRelativePath(e.Location);
        StateHasChanged();
    }

    public void Dispose()
    {
        NavigationManager.LocationChanged -= OnLocationChanged;
    }

}