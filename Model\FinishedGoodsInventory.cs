using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace JayERP.Model
{
    public class FinishedGoodsInventory : BaseEntity
    {
        [Required]
        public int ProductId { get; set; }
        
        [Required]
        [MaxLength(100)]
        public string Location { get; set; } = string.Empty;
        
        [MaxLength(50)]
        public string BatchNumber { get; set; } = string.Empty;
        
        public DateTime? ExpiryDate { get; set; }
        
        public DateTime ProductionDate { get; set; }
        
        [Range(0, double.MaxValue)]
        public decimal QuantityOnHand { get; set; }
        
        [Range(0, double.MaxValue)]
        public decimal ReservedQuantity { get; set; }
        
        [Range(0, double.MaxValue)]
        public decimal AvailableQuantity { get; set; }
        
        [Range(0, double.MaxValue)]
        public decimal UnitCost { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalValue { get; set; }
        
        public DateTime LastMovementDate { get; set; } = DateTime.UtcNow;
        
        [MaxLength(500)]
        public string QualityStatus { get; set; } = string.Empty;
        
        [MaxLength(1000)]
        public string Notes { get; set; } = string.Empty;
        
        // Navigation Properties
        [ForeignKey("ProductId")]
        public virtual Product Product { get; set; } = null!;
    }
}
