using JayERP.Data;
using JayERP.DTOs;
using JayERP.Model;
using Microsoft.EntityFrameworkCore;

namespace JayERP.Services
{
    public class VendorService : IVendorService
    {
        private readonly ApplicationDbContext _context;

        public VendorService(ApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<IEnumerable<VendorListDto>> GetAllVendorsAsync()
        {
            return await _context.Vendors
                .Where(v => v.IsActive)
                .Select(v => new VendorListDto
                {
                    Id = v.Id,
                    Name = v.Name,
                    CompanyName = v.CompanyName,
                    Category = v.Category.ToString(),
                    Email = v.Email,
                    Phone = v.Phone,
                    City = v.City,
                    PaymentTerms = v.PaymentTerms.ToString(),
                    CreditLimit = v.CreditLimit,
                    QualityRating = v.QualityRating,
                    DeliveryRating = v.DeliveryRating,
                    IsActive = v.IsActive,
                    CreatedDate = v.CreatedDate
                })
                .OrderBy(v => v.Name)
                .ToListAsync();
        }

        public async Task<VendorDto?> GetVendorByIdAsync(int id)
        {
            var vendor = await _context.Vendors
                .FirstOrDefaultAsync(v => v.Id == id && v.IsActive);

            if (vendor == null) return null;

            return new VendorDto
            {
                Id = vendor.Id,
                Name = vendor.Name,
                CompanyName = vendor.CompanyName,
                Category = vendor.Category,
                Address = vendor.Address,
                City = vendor.City,
                State = vendor.State,
                PostalCode = vendor.PostalCode,
                Country = vendor.Country,
                Email = vendor.Email,
                Phone = vendor.Phone,
                Mobile = vendor.Mobile,
                ContactPerson = vendor.ContactPerson,
                TaxNumber = vendor.TaxNumber,
                RegistrationNumber = vendor.RegistrationNumber,
                PaymentTerms = vendor.PaymentTerms,
                CreditLimit = vendor.CreditLimit,
                QualityRating = vendor.QualityRating,
                DeliveryRating = vendor.DeliveryRating,
                Notes = vendor.Notes,
                IsActive = vendor.IsActive,
                CreatedDate = vendor.CreatedDate,
                ModifiedDate = vendor.ModifiedDate,
                CreatedBy = vendor.CreatedBy,
                ModifiedBy = vendor.ModifiedBy
            };
        }

        public async Task<VendorDto> CreateVendorAsync(VendorCreateDto vendorCreateDto, string createdBy)
        {
            var vendor = new Vendor
            {
                Name = vendorCreateDto.Name,
                CompanyName = vendorCreateDto.CompanyName,
                Category = vendorCreateDto.Category,
                Address = vendorCreateDto.Address,
                City = vendorCreateDto.City,
                State = vendorCreateDto.State,
                PostalCode = vendorCreateDto.PostalCode,
                Country = vendorCreateDto.Country,
                Email = vendorCreateDto.Email,
                Phone = vendorCreateDto.Phone,
                Mobile = vendorCreateDto.Mobile,
                ContactPerson = vendorCreateDto.ContactPerson,
                TaxNumber = vendorCreateDto.TaxNumber,
                RegistrationNumber = vendorCreateDto.RegistrationNumber,
                PaymentTerms = vendorCreateDto.PaymentTerms,
                CreditLimit = vendorCreateDto.CreditLimit,
                QualityRating = vendorCreateDto.QualityRating,
                DeliveryRating = vendorCreateDto.DeliveryRating,
                Notes = vendorCreateDto.Notes,
                CreatedBy = createdBy,
                CreatedDate = DateTime.UtcNow,
                IsActive = true
            };

            _context.Vendors.Add(vendor);
            await _context.SaveChangesAsync();

            return await GetVendorByIdAsync(vendor.Id) ?? throw new InvalidOperationException("Failed to retrieve created vendor");
        }

        public async Task<VendorDto> UpdateVendorAsync(VendorUpdateDto vendorUpdateDto, string modifiedBy)
        {
            var vendor = await _context.Vendors
                .FirstOrDefaultAsync(v => v.Id == vendorUpdateDto.Id && v.IsActive);

            if (vendor == null)
                throw new ArgumentException("Vendor not found");

            vendor.Name = vendorUpdateDto.Name;
            vendor.CompanyName = vendorUpdateDto.CompanyName;
            vendor.Category = vendorUpdateDto.Category;
            vendor.Address = vendorUpdateDto.Address;
            vendor.City = vendorUpdateDto.City;
            vendor.State = vendorUpdateDto.State;
            vendor.PostalCode = vendorUpdateDto.PostalCode;
            vendor.Country = vendorUpdateDto.Country;
            vendor.Email = vendorUpdateDto.Email;
            vendor.Phone = vendorUpdateDto.Phone;
            vendor.Mobile = vendorUpdateDto.Mobile;
            vendor.ContactPerson = vendorUpdateDto.ContactPerson;
            vendor.TaxNumber = vendorUpdateDto.TaxNumber;
            vendor.RegistrationNumber = vendorUpdateDto.RegistrationNumber;
            vendor.PaymentTerms = vendorUpdateDto.PaymentTerms;
            vendor.CreditLimit = vendorUpdateDto.CreditLimit;
            vendor.QualityRating = vendorUpdateDto.QualityRating;
            vendor.DeliveryRating = vendorUpdateDto.DeliveryRating;
            vendor.Notes = vendorUpdateDto.Notes;
            vendor.IsActive = vendorUpdateDto.IsActive;
            vendor.ModifiedBy = modifiedBy;
            vendor.ModifiedDate = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            return await GetVendorByIdAsync(vendor.Id) ?? throw new InvalidOperationException("Failed to retrieve updated vendor");
        }

        public async Task<bool> DeleteVendorAsync(int id)
        {
            var vendor = await _context.Vendors.FindAsync(id);
            if (vendor == null) return false;

            // Soft delete
            vendor.IsActive = false;
            vendor.ModifiedDate = DateTime.UtcNow;

            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> VendorExistsAsync(int id)
        {
            return await _context.Vendors.AnyAsync(v => v.Id == id && v.IsActive);
        }

        public async Task<bool> IsEmailUniqueAsync(string email, int? excludeId = null)
        {
            var query = _context.Vendors.Where(v => v.Email == email && v.IsActive);
            
            if (excludeId.HasValue)
                query = query.Where(v => v.Id != excludeId.Value);

            return !await query.AnyAsync();
        }

        public async Task<IEnumerable<VendorListDto>> SearchVendorsAsync(string searchTerm)
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
                return await GetAllVendorsAsync();

            var lowerSearchTerm = searchTerm.ToLower();

            return await _context.Vendors
                .Where(v => v.IsActive && 
                    (v.Name.ToLower().Contains(lowerSearchTerm) ||
                     v.CompanyName.ToLower().Contains(lowerSearchTerm) ||
                     v.Email.ToLower().Contains(lowerSearchTerm) ||
                     v.ContactPerson.ToLower().Contains(lowerSearchTerm)))
                .Select(v => new VendorListDto
                {
                    Id = v.Id,
                    Name = v.Name,
                    CompanyName = v.CompanyName,
                    Category = v.Category.ToString(),
                    Email = v.Email,
                    Phone = v.Phone,
                    City = v.City,
                    PaymentTerms = v.PaymentTerms.ToString(),
                    CreditLimit = v.CreditLimit,
                    QualityRating = v.QualityRating,
                    DeliveryRating = v.DeliveryRating,
                    IsActive = v.IsActive,
                    CreatedDate = v.CreatedDate
                })
                .OrderBy(v => v.Name)
                .ToListAsync();
        }

        public async Task<IEnumerable<VendorListDto>> GetVendorsByCategoryAsync(VendorCategory category)
        {
            return await _context.Vendors
                .Where(v => v.IsActive && v.Category == category)
                .Select(v => new VendorListDto
                {
                    Id = v.Id,
                    Name = v.Name,
                    CompanyName = v.CompanyName,
                    Category = v.Category.ToString(),
                    Email = v.Email,
                    Phone = v.Phone,
                    City = v.City,
                    PaymentTerms = v.PaymentTerms.ToString(),
                    CreditLimit = v.CreditLimit,
                    QualityRating = v.QualityRating,
                    DeliveryRating = v.DeliveryRating,
                    IsActive = v.IsActive,
                    CreatedDate = v.CreatedDate
                })
                .OrderBy(v => v.Name)
                .ToListAsync();
        }

        public async Task<VendorPerformanceDto?> GetVendorPerformanceAsync(int vendorId)
        {
            var vendor = await _context.Vendors.FindAsync(vendorId);
            if (vendor == null) return null;

            // Calculate actual performance metrics from purchase orders
            var purchaseOrders = await _context.PurchaseOrders
                .Where(po => po.VendorId == vendorId)
                .ToListAsync();

            var totalOrders = purchaseOrders.Count;
            var totalAmount = purchaseOrders.Sum(po => po.TotalAmount);
            var lastOrderDate = purchaseOrders.Any() ? purchaseOrders.Max(po => po.OrderDate) : DateTime.MinValue;

            // Calculate on-time deliveries (simplified - would need goods receipts data)
            var completedOrders = purchaseOrders.Where(po => po.Status == PurchaseOrderStatus.FullyReceived).Count();
            var onTimeDeliveryPercentage = totalOrders > 0 ? (decimal)completedOrders / totalOrders * 100 : 0;

            return new VendorPerformanceDto
            {
                VendorId = vendor.Id,
                VendorName = vendor.Name,
                QualityRating = vendor.QualityRating,
                DeliveryRating = vendor.DeliveryRating,
                TotalOrders = totalOrders,
                OnTimeDeliveries = completedOrders,
                QualityIssues = 0, // TODO: Calculate from goods receipt quality data
                OnTimeDeliveryPercentage = onTimeDeliveryPercentage,
                QualityPercentage = vendor.QualityRating * 20, // Convert 1-5 rating to percentage
                AverageDeliveryDays = 0, // TODO: Calculate from actual delivery data
                TotalPurchaseAmount = totalAmount,
                LastOrderDate = lastOrderDate
            };
        }

        public async Task<IEnumerable<VendorPerformanceDto>> GetTopVendorsByPerformanceAsync(int count = 10)
        {
            return await _context.Vendors
                .Where(v => v.IsActive)
                .OrderByDescending(v => (v.QualityRating + v.DeliveryRating) / 2.0)
                .Take(count)
                .Select(v => new VendorPerformanceDto
                {
                    VendorId = v.Id,
                    VendorName = v.Name,
                    QualityRating = v.QualityRating,
                    DeliveryRating = v.DeliveryRating,
                    // TODO: Implement actual performance calculations
                    TotalOrders = 0,
                    OnTimeDeliveries = 0,
                    QualityIssues = 0,
                    OnTimeDeliveryPercentage = 0,
                    QualityPercentage = 0,
                    AverageDeliveryDays = 0,
                    TotalPurchaseAmount = 0,
                    LastOrderDate = DateTime.MinValue
                })
                .ToListAsync();
        }

        public async Task UpdateVendorRatingAsync(int vendorId, int qualityRating, int deliveryRating)
        {
            var vendor = await _context.Vendors.FindAsync(vendorId);
            if (vendor == null) return;

            vendor.QualityRating = qualityRating;
            vendor.DeliveryRating = deliveryRating;
            vendor.ModifiedDate = DateTime.UtcNow;

            await _context.SaveChangesAsync();
        }
    }
}
