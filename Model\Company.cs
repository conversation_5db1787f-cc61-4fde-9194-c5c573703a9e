using System.ComponentModel.DataAnnotations;

namespace JayERP.Model
{
    public class Company : BaseEntity
    {
        [Required]
        [MaxLength(200)]
        public string Name { get; set; } = string.Empty;
        
        [MaxLength(200)]
        public string LegalName { get; set; } = string.Empty;
        
        [Required]
        [MaxLength(500)]
        public string Address { get; set; } = string.Empty;
        
        [MaxLength(100)]
        public string City { get; set; } = string.Empty;
        
        [MaxLength(50)]
        public string State { get; set; } = string.Empty;
        
        [MaxLength(20)]
        public string PostalCode { get; set; } = string.Empty;
        
        [MaxLength(50)]
        public string Country { get; set; } = string.Empty;
        
        [Required]
        [EmailAddress]
        [MaxLength(100)]
        public string Email { get; set; } = string.Empty;
        
        [MaxLength(20)]
        public string Phone { get; set; } = string.Empty;
        
        [MaxLength(20)]
        public string Fax { get; set; } = string.Empty;
        
        [MaxLength(100)]
        public string Website { get; set; } = string.Empty;
        
        [MaxLength(50)]
        public string TaxNumber { get; set; } = string.Empty;
        
        [MaxLength(50)]
        public string GSTNumber { get; set; } = string.Empty;
        
        [MaxLength(50)]
        public string PANNumber { get; set; } = string.Empty;
        
        [MaxLength(50)]
        public string RegistrationNumber { get; set; } = string.Empty;
        
        [MaxLength(50)]
        public string CINNumber { get; set; } = string.Empty;
        
        [MaxLength(100)]
        public string BankName { get; set; } = string.Empty;
        
        [MaxLength(50)]
        public string BankAccountNumber { get; set; } = string.Empty;
        
        [MaxLength(20)]
        public string BankIFSC { get; set; } = string.Empty;
        
        [MaxLength(50)]
        public string Currency { get; set; } = "INR";
        
        [MaxLength(10)]
        public string CurrencySymbol { get; set; } = "₹";
        
        [MaxLength(50)]
        public string FinancialYearStart { get; set; } = "April";
        
        [MaxLength(200)]
        public string LogoPath { get; set; } = string.Empty;
        
        [MaxLength(1000)]
        public string Notes { get; set; } = string.Empty;
    }
}
