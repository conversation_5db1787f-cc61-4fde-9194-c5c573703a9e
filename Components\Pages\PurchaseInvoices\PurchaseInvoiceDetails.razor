@using JayERP.DTOs
@using JayERP.Services
@using Syncfusion.Blazor.Grids
@using Syncfusion.Blazor.Buttons

@inject IPurchaseInvoiceService PurchaseInvoiceService
@inject IJSRuntime JSRuntime

@if (purchaseInvoice != null)
{
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Purchase Invoice Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label fw-bold">Invoice Number:</label>
                                    <p class="mb-0">@purchaseInvoice.InvoiceNumber</p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label fw-bold">Vendor:</label>
                                    <p class="mb-0">@purchaseInvoice.VendorName</p>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label fw-bold">Vendor Invoice Number:</label>
                                    <p class="mb-0">@purchaseInvoice.VendorInvoiceNumber</p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label fw-bold">Invoice Date:</label>
                                    <p class="mb-0">@purchaseInvoice.InvoiceDate.ToString("MMM dd, yyyy")</p>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label fw-bold">Due Date:</label>
                                    <p class="mb-0">@purchaseInvoice.DueDate.ToString("MMM dd, yyyy")</p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label fw-bold">Payment Terms:</label>
                                    <p class="mb-0">@purchaseInvoice.PaymentTerms.ToString()</p>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label fw-bold">Status:</label>
                                    <span class="badge @GetStatusBadgeClass(purchaseInvoice.Status.ToString()) fs-6">
                                        @purchaseInvoice.Status.ToString()
                                    </span>
                                </div>
                            </div>
                            @if (!string.IsNullOrEmpty(purchaseInvoice.PurchaseOrderNumber))
                            {
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">Purchase Order:</label>
                                        <p class="mb-0">@purchaseInvoice.PurchaseOrderNumber</p>
                                    </div>
                                </div>
                            }
                        </div>

                        @if (!string.IsNullOrEmpty(purchaseInvoice.GoodsReceiptNumber))
                        {
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">Goods Receipt:</label>
                                        <p class="mb-0">@purchaseInvoice.GoodsReceiptNumber</p>
                                    </div>
                                </div>
                            </div>
                        }

                        @if (!string.IsNullOrEmpty(purchaseInvoice.Notes))
                        {
                            <div class="row">
                                <div class="col-12">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">Notes:</label>
                                        <p class="mb-0">@purchaseInvoice.Notes</p>
                                    </div>
                                </div>
                            </div>
                        }
                    </div>
                </div>

                <!-- Purchase Invoice Items -->
                <div class="card mt-3">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Invoice Items</h5>
                    </div>
                    <div class="card-body">
                        @if (purchaseInvoice.Items.Any())
                        {
                            <SfGrid DataSource="@purchaseInvoice.Items"
                                    AllowPaging="false"
                                    Height="400px">
                                <GridColumns>
                                    <GridColumn Field="@nameof(PurchaseInvoiceItemDto.MaterialCode)" HeaderText="Material Code" Width="120"></GridColumn>
                                    <GridColumn Field="@nameof(PurchaseInvoiceItemDto.MaterialName)" HeaderText="Material Name" Width="200"></GridColumn>
                                    <GridColumn Field="@nameof(PurchaseInvoiceItemDto.Quantity)" HeaderText="Quantity" Width="100" Format="N2" TextAlign="TextAlign.Right"></GridColumn>
                                    <GridColumn Field="@nameof(PurchaseInvoiceItemDto.UnitPrice)" HeaderText="Unit Price" Width="120" Format="C2" TextAlign="TextAlign.Right"></GridColumn>
                                    <GridColumn Field="@nameof(PurchaseInvoiceItemDto.LineTotal)" HeaderText="Line Total" Width="120" Format="C2" TextAlign="TextAlign.Right"></GridColumn>
                                    <GridColumn Field="@nameof(PurchaseInvoiceItemDto.DiscountRate)" HeaderText="Disc %" Width="80" Format="N1" TextAlign="TextAlign.Right"></GridColumn>
                                    <GridColumn Field="@nameof(PurchaseInvoiceItemDto.TaxRate)" HeaderText="Tax %" Width="80" Format="N1" TextAlign="TextAlign.Right"></GridColumn>
                                    <GridColumn Field="@nameof(PurchaseInvoiceItemDto.NetAmount)" HeaderText="Net Amount" Width="120" Format="C2" TextAlign="TextAlign.Right"></GridColumn>
                                </GridColumns>
                            </SfGrid>
                        }
                        else
                        {
                            <div class="text-center py-4">
                                <p class="text-muted">No items found for this purchase invoice.</p>
                            </div>
                        }
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                <!-- Invoice Summary -->
                <div class="card">
                    <div class="card-header">
                        <h6 class="card-title mb-0">Invoice Summary</h6>
                    </div>
                    <div class="card-body">
                        <div class="d-flex justify-content-between mb-2">
                            <span>Subtotal:</span>
                            <span>@purchaseInvoice.SubTotal.ToString("C2")</span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>Discount:</span>
                            <span>@purchaseInvoice.DiscountAmount.ToString("C2")</span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>Tax Amount:</span>
                            <span>@purchaseInvoice.TaxAmount.ToString("C2")</span>
                        </div>
                        <hr>
                        <div class="d-flex justify-content-between fw-bold fs-5">
                            <span>Total Amount:</span>
                            <span>@purchaseInvoice.TotalAmount.ToString("C2")</span>
                        </div>
                        <hr>
                        <div class="d-flex justify-content-between mb-2">
                            <span>Paid Amount:</span>
                            <span>@purchaseInvoice.PaidAmount.ToString("C2")</span>
                        </div>
                        <div class="d-flex justify-content-between fw-bold text-danger">
                            <span>Balance Due:</span>
                            <span>@purchaseInvoice.BalanceAmount.ToString("C2")</span>
                        </div>
                    </div>
                </div>

                <!-- Invoice Status -->
                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="card-title mb-0">Invoice Status</h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <span class="badge @GetStatusBadgeClass(purchaseInvoice.Status.ToString()) fs-6">
                                @purchaseInvoice.Status.ToString()
                            </span>
                        </div>
                        
                        <div class="mb-2">
                            <small class="text-muted">Created: @purchaseInvoice.CreatedDate.ToString("MMM dd, yyyy")</small>
                        </div>
                        
                        @if (purchaseInvoice.ModifiedDate.HasValue)
                        {
                            <div class="mb-2">
                                <small class="text-muted">Modified: @purchaseInvoice.ModifiedDate.Value.ToString("MMM dd, yyyy")</small>
                            </div>
                        }
                        
                        <div class="mb-2">
                            <small class="text-muted">Created by: @purchaseInvoice.CreatedBy</small>
                        </div>
                        
                        @if (!string.IsNullOrEmpty(purchaseInvoice.ModifiedBy))
                        {
                            <div class="mb-2">
                                <small class="text-muted">Modified by: @purchaseInvoice.ModifiedBy</small>
                            </div>
                        }

                        @if (purchaseInvoice.Status == Model.InvoiceStatus.Overdue)
                        {
                            <div class="mt-3">
                                <div class="alert alert-danger">
                                    <strong>Overdue!</strong><br />
                                    This invoice is past its due date.
                                </div>
                            </div>
                        }
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="card-title mb-0">Quick Actions</h6>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            @if (purchaseInvoice.Status == Model.InvoiceStatus.Draft)
                            {
                                <SfButton @onclick="() => SendPurchaseInvoice()"
                                          CssClass="e-primary"
                                          IconCss="e-icons e-send">
                                    Send Invoice
                                </SfButton>
                            }
                            
                            @if (purchaseInvoice.BalanceAmount > 0 && 
                                 (purchaseInvoice.Status == Model.InvoiceStatus.Sent || 
                                  purchaseInvoice.Status == Model.InvoiceStatus.PartiallyPaid ||
                                  purchaseInvoice.Status == Model.InvoiceStatus.Overdue))
                            {
                                <SfButton @onclick="() => RecordPayment()"
                                          CssClass="e-success"
                                          IconCss="e-icons e-plus">
                                    Record Payment
                                </SfButton>
                            }
                            
                            <SfButton @onclick="() => PrintPurchaseInvoice()"
                                      CssClass="e-outline"
                                      IconCss="e-icons e-print">
                                Print Invoice
                            </SfButton>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
}
else if (isLoading)
{
    <div class="text-center p-4">
        <div class="spinner-border" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
        <p class="mt-2">Loading purchase invoice details...</p>
    </div>
}
else
{
    <div class="text-center p-4">
        <p class="text-muted">Purchase invoice not found.</p>
    </div>
}

@code {
    [Parameter] public int PurchaseInvoiceId { get; set; }

    private PurchaseInvoiceDto? purchaseInvoice;
    private bool isLoading = true;

    protected override async Task OnParametersSetAsync()
    {
        if (PurchaseInvoiceId > 0)
        {
            await LoadPurchaseInvoiceDetails();
        }
    }

    private async Task LoadPurchaseInvoiceDetails()
    {
        isLoading = true;
        try
        {
            purchaseInvoice = await PurchaseInvoiceService.GetPurchaseInvoiceByIdAsync(PurchaseInvoiceId);
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"Error loading purchase invoice details: {ex.Message}");
        }
        finally
        {
            isLoading = false;
        }
    }

    private string GetStatusBadgeClass(string status)
    {
        return status switch
        {
            "Draft" => "bg-secondary",
            "Sent" => "bg-info",
            "Paid" => "bg-success",
            "PartiallyPaid" => "bg-warning",
            "Overdue" => "bg-danger",
            "Cancelled" => "bg-dark",
            "Refunded" => "bg-primary",
            _ => "bg-secondary"
        };
    }

    private async Task SendPurchaseInvoice()
    {
        try
        {
            await PurchaseInvoiceService.SendPurchaseInvoiceAsync(PurchaseInvoiceId, "CurrentUser");
            await LoadPurchaseInvoiceDetails();
            await JSRuntime.InvokeVoidAsync("alert", "Purchase invoice sent successfully!");
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"Error sending purchase invoice: {ex.Message}");
        }
    }

    private async Task RecordPayment()
    {
        await JSRuntime.InvokeVoidAsync("alert", "Record payment functionality will be implemented.");
    }

    private async Task PrintPurchaseInvoice()
    {
        await JSRuntime.InvokeVoidAsync("alert", "Print functionality will be implemented.");
    }
}
