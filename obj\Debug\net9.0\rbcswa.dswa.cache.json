{"GlobalPropertiesHash": "2ilJ2M8+ZdH0swl4cXFj9Ji8kay0R08ISE/fEc+OL0o=", "FingerprintPatternsHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["Yp6Na6OHuAM9319B7rpHlJcqMSiWcANGtD8qC1WRyzA=", "jpyHQsU+D+mgb2Y6mJ9lGxCiXyze56dQ/VyKxro3uCE=", "/hgI/5RVDv+IUV4tWah6nq/nnqOla7MN0//mIq0bB3M=", "VOuIqqxjjYkoYkWhL/9qYmXwjAwxnZpVbPKl//VNCW8=", "3wW2VJb8JZUBN+uPgAW4E5lXIa09NLk1S/UFPCaOv8Q=", "MF0lXqoMz5irgbSEBh6oabdRX/cqhzZaotObo7VcewE=", "6sG5RM4C8xsLV5b9jpbEk7B4pyiiFpqt+CuLrwoRVCw=", "Abin/FezBWpSH//xK9nO77KfFTNr7VwkfItDnf+PPU4=", "2t9pFxkJnjxn3Mk6/xB1xfb22aPbRL9ztVm9TmJdN8w=", "q2J1bmrHsKwB/y+IcQzO9NQJBAqAL2wLrpH57OuuawQ=", "nhTwtmBufG1mx5ZAZb0lb9nR0tdNYjWxjFpw0zvFSis=", "osZpFnH60p+UWBtGNDbF6aIE5DbkFX0zUVhoOLcF64I=", "JlN9hzWDpaRinmOZpI9boHuRcZgU4NV1H8UQF1YQ+as=", "JH+d5oIlexS5TXCNTmEFIle1kwW/6Q6CTyS+cXzpfks=", "4ZtSa42/cMU5SDIL6EVZCRcmckY+lEfbtPYrS841P3Y=", "j95nYS82epFhJ9MpquHCVnW/Q6S/m1aYVJOq5uPfoEQ=", "Tnf/Gvgd8WOsx9EXYRlb88JTmgCB2Ize3P0saiDHP/s=", "HMxWN6mYG3Z57y3VDJvxaySm94m5V8sM3pp28SrKHr4=", "5jqjA979Y4obmpgj4SLZPprrgyYt6wLnNEP9NsZgxQo=", "LhfTRnjJM/+FTW8TZk3/ycCGFsV1hn/NcSofqZLzmgc=", "FtijI6V2GHh+MChHKpcymZlFVtsNEdnhJ533itSAcOY=", "wMEk3F7U3ynAO2m20pODiR+MI3JnGM5PjsipNz5ACmQ=", "ox+DUq2ZWBAa8dqLipXDobMv24LEMBUB9FlBMVUTWM8=", "AQd9ciffbqPuwKEBuj//cwtI2sN2GMDR1LGzSYhVd0Y=", "ntbKhxkR4+faRsGhm1RvugKOdy5KjgQI64ri+l0w5as=", "y6uIJcHWm1J85rpHM4yW7eQv/Q6+GWRfsOj13VFnt7w=", "kAmxIXaZIF0OavnpYU71LzX7jTk0pmcrWs7Y+JMa/0Q=", "Y6XufGouw4TSGucs0UWc/Z4SnuFLHLhemZNvv49vetQ=", "WiENPxED3c/FO0R1K2ifsYVDgK6woCUlrYl9Va8CYco=", "qLr+Xp+7aKC1XN6AOpQN+IvvURj50A31UI88PVTE7Eg=", "hMcpn9jPwQHeorvUZZpOxt3Bqi0ClZKlE2yw/EdCPJ8=", "bVrG2xP2SZIQzfOWSBnNvhPlWO/G9Ey2ruoE4n3YFN8=", "pmgWx8k5qjo/HJszze9yafqSWah8440ubYt+Q2q8D04=", "/8QG+N6sA7gQ9CQxTjqpOoJ/hgNsTeoVztauvYhwQsM=", "icwaKbSciJw6JjZ+hUPFhnPKfjAEbEXQo3jE5Vc6Pvw=", "nOo0gfiElFRwW7fPUQqDgOuwTIT2PQtxwg8GEaEdmvg=", "iCmdtj6TU3Z7vpm5qpA5gCYWUrmY5IdPNa28vuNDpRY=", "X8tXR9vKH9w9nRLFdzzA9ZYxbtWtt5QcP6BJ1S+J94g=", "Z66HuAnGXrOfnEf62jS9v3YcEUp/fSWk5C63Pva/Owo=", "FKF9COdQxPJ4DRQMbkqmrAnrrvWb/6gMpjCAnWqK2Mk=", "C1PGKvh+Hg1aIWoJJ0IFr+TrbLADSiXpwWC947TRWUg=", "80uJYTbthenKH7RBkTP7r8AThBF6FJ96cYqs9xq50bI=", "umfhERbKIWQhJa9s5P9gHdnTGhDHP864yUY/XU5zZOg=", "NOC1+JrTC+Wea4NcQQ6PlhDylbd/6YtOIJpfIU24HB0=", "NqCMNCTmYGBnhF0xbMAAAhD5PN5VDf9A2UftaEjpkNw=", "XKNuml1hCJ5rz+5sAtL3WhgeKSoXY3g6iIkg/W2+RH0=", "3L2Hkf38q+OSeG4DKB7OSVcSfZYQOA+eDzbIrrIUnCU=", "GkDCQzccyk0RxmkfViQ61kgulmouwT5axzv5WISBvUY=", "9xqJg0r4l+WtqLd6N+rncpNF4cHw5n71lcZ7WZoX3iE=", "q+rPxkxhTda2It40OPqpq1VEhIEvgKbOFTPKvun+KCo=", "Vg2/DyssOuQ7UiLgnn1FSr9qwjqsIphTgQj8S82N0ac=", "MHMkhr3He4PXsip8LKAP/kXBkvCzs6CLudYVS8qvloA=", "ygDIehyDAlySsDhWozKT0ds40y8+aqh+/KlcjsjXwoA=", "7BzyxV11vX/HTXBvQrPbbEyQMP+PrfGdfPPx8O0W5cE=", "eAKEUeQxo4P2OY4Yo0E3xb/2NsAAygGpcxjHvr4Eki8=", "u/UvCTQgASUpF7+0VRX94+gBBaH8IwxG8c+BL/Gbmgg=", "HXpWftpHD1l3KGIEQTYeaAwi7So0lNOvlcl24NR6aGI=", "A6JGlNVixLQOyu5jJjgd4t1lQNRuyNCowxH1/m2Tb70=", "29ape4XKJkZZKEd/PLfca/8RymAbRtqedtwnE1pcubc=", "/01MpjBIBlT64Iuwtb7LPSBJaH4sE9B2jK9l7e7RIQo=", "crG3HCNKM91KSNyW86Rd6/SwYvH7n5PvIFHJbOTvqSU=", "gUSXYPO0siJcHxtfFCsn55XY6ahDZhe11wejD/JtJSc=", "+PaZeDLRCJ4mATxVENPYETAg27Q0jkan2gwJtqpz3JQ=", "dvoGju/xFXowvL1Ddsx4fm/ZXq1giq5qbqgA9f1bE/I=", "S8lBV6xXJmg6SyfRgvZ/eS4kWZ5SvrwR8NSM/zLOmG4=", "oPnypIjT7psBuADWWvQvROl8AaT6YXqWoG/ipXsYF1o=", "ZWkTc+PKPvZjVMEmqipsfRW1wKG3t8yNaMgtX8p24Sw=", "MaBKL184A8qRn/YXtONfAA47DMYhWTyG4GCPCcbwNrg=", "xxU6am8z3nm59H11T930Zj6j8vi+H39PSkF121F/Kxk=", "aMUjw5uAcp8MrNj5KXrt7uI5c3PKSL+orAkQj/v4jJc=", "qfX8uCcJjNIDgR+7Z/TJzQDzTHDFk3x6Q4dIxfs5zA4=", "AgR+OdOHd2O3QP/Szvk13qNJwBKjo29bfwy+dqMYJtE=", "WGNtphC9FUZDrns8QHp/Pb8Dmekj6yJ7SpXYAIPLbdk=", "GyAx/eisb0k5R0qwwfLJrTDM3lNtacdqZfIyG8vdBHg=", "TtajcYiY8P8YlssnI3LFTpOsSdPVb1yP41em92X+8Yg=", "9xBqMnyu9b9M3ZB8E+zKEf34ciqaOBX8AF/6xnlUQuM=", "6t0SobkkmgqdQiKsbiwrehQrm5195jFScoB6KFnNU0g=", "4XKAbZO4VJVMqrVEBHfQHJ69meR6iGkpsCbWUt6xHuI=", "LWIFSLvsoGxPAwZacbBQHL6Cbo/RI/MKntETsCVASrQ=", "uPNZqI6FB2fG7noOUduyTI8bG6FzguRMaUGtyrF68kg=", "RpcJCm+MfWM11y38ZaxcU6THvC0tLXxyCY+crLM4jac=", "UoS6UjRJjIsYSBJ5aG4+OLV/Rz+4RHqLtpOhTRYcAAk=", "+u+dAqheiEV8tay7Bnsnw3DAKfO5Tr/S1x2rsaI1Dnk=", "c+slbXDwuZuJhf5DoHxYrmBVGkghkBTsKEZ4+Qj9e+M=", "Dw1FrCZIiWXdu+efX8jfEBA6OSIN+5TQ1tMDZBF1dys=", "MhjEl0yivagtkbJiJP0BgVnthjchcaRKQpuRdKs58hs=", "rnifI1hozxp57LGYJKDn/ZFKKHjWF760Cco2bsae0XI=", "PKIY7XlVqtOuOR+eq7HzUohDVSoWR5rF1E8ASpx6yJo=", "6HPGFAyBHKYxtj+mXMJM3hliPvWQZqhGmKp1XtzRbN8=", "Xnsrj87teDxSrMI0t6IJBM9wxmO1/wl01y9mQx7wN2c=", "NPniCEb2fe8MULe/mDmF6oDCccRqNivdLMWyhVNXIeE=", "FGc8CLKjvehCpvldV0vW0PgP0+ijZY607GDuPLudb/s=", "LPyXRE5JrLzSW/4A+exHtmmKp/j0hSk+0FenPM6x4mg=", "ri2At4HKO7VQ7D1Q681PDCjbBqV8413wrYTPwXARTYM=", "5fQKHoflODBSTWw0SsKr6LlRM/jwxViQgVJq3PpfWSg=", "qMRYlsQg8NXbmNCBsbzQq3C+MzV4fiQ8i+6hu++p41A=", "X+ET1tJ4sAkJiO0EQMzMoUI0GWpeJ/nxaVvV5jyh8lU=", "+w8bfmL+nf/NuEpJHy5WsOEEp+GpRxbVJknHa6MXL2Y=", "OHmnyFzMr3NGqu3S4OXY4fcEzBwRaX+g4tfCMpnPZf8=", "Poodhr9XcFj7lAnZmYCOre0i6pU4E/t6YiybUbFhaaI=", "huxcRYvH829ie63l0xDTt7PI8VWuOOiR0if1xm0xhmI=", "souuQC32KgksJ/tbnU//9M5aMhGGK0JXSLqc/XYCiMw=", "PuinYp68gt4Y9DXqhrY0hGDrUTAiKhlICRTSPD/EyLM=", "/hd1cfg7FKoVwzySAYeqWJ0guYw+6fFwq8+ai3j47TM=", "PKQm4EOBapAPm4dxlhE1OnPeNj1gDhBfYHDd0SNUR9k=", "cN277LZiwnB5TmQgH0O2NVlIrj7naqmI91D95JM7TXA=", "G64Yj8wEiEYtm5pq8FQcfnbFv9u7ksjNo3a8j4v+8N4=", "rtanUDQ3MXzM72b/pSJCTfOW4Krc2Eh5P/Vx/fRu0yI=", "AvJLkrtIC6c9QFqRkN05IXdsEm4eVvO5wDNeLUFKO2o=", "FsMKwrZ/U8uAF8XQe1Qppr2lpcM5d8hnGyaleZOmqQo=", "SgJpKPUv9ZmBpZrDC+UvygBEkkhSqWfLU6PXMdorZTQ=", "vD3/AmqAYRoDkW/vgGY6b0bU29F06kTvqdhk1hSc4Kc=", "WPiHjzUlBq3x8FSH2M43HCedhuuuM54U5Pb+/ZvkXrg=", "VdSshficBVsDctGUlb0cuTzgRLW6lUtXIq8RuBV1OR0=", "xdAtFfre4YLrrErubwRPtlZ5N8jorlsRMazdj/dkpCI=", "kieRqLlPXo70ZAq2+AGV5NRjktASOwv0ABCASfcFPss=", "4Z3lvJJYg0+ymkbCB37pHJSUE7LZnjTQ0BtHkC6srIk=", "Mv2H7mJCyjVfcUKuqn88JDO/n74eVNyxycTBioD0+jM=", "Id0aMIQEAckrD65sSbZgiqwrjpKwWw9I2UHKKuM4eQU=", "nEgXvQ4Mw2IEyoo4yaq8WCX5Cpa4QHy3gA5ghreF+uA=", "P8z10h0OjlsgILNGlPE7bqP5gPk+RjtlpRB3JGJvuvc=", "IbybrlVMS8bEnDQi2BuW8RBq2o+v4J7CQioazMKIfuw=", "mlCB7hr15VHoAD6DUhGUJBxrynAwTtfR7yLS+nUTp7c=", "tTwX58GdyzRbJ7wlTruCVIx45r53FRokcWlQ/KOz+Z4=", "dOVM6T6eU6ERQRgiBR99UgNhYpmW6wi/y+tK/1eeB10=", "ILTv2Ou7bfcnS4mwaDnJKV1Mv2AHi6KqV/SgIa4hx6Q=", "AMslRGLCJmX6ANXP/7+qZ+sELqPH3+F6k64c8GjyvmU=", "CuA1Fjl9TjXBw40cHJxq7P59Ukjem9MNUPGumOqdO8o=", "Rlxpzuf/7kg3izvQ1e8pnQf7l5Px1LXUkn1BRUPGkCc=", "iI1ZD7SfYyuYGLwvjmqWYSNrENRr1Z/9AZOeFx4dyGM=", "mwlTia4rb2RBVVsrdzfLbDRy3+B6+0YGOcYjmt26HzE=", "M0O5C81BeGOlC1Vd7HOReRBXwUuuQCIw05FkRbB5w3k=", "5dKUOdBcfTotvbHYIoIgM831ph/5gq0Sb8s7/IgiDAY=", "o0oXem58lutb9NIcCDGg7R6D74pVbG9OdTXLBgOfvWU=", "QrfoU9BMHT18xrqoSCIOzOwYCoeD3vMGjoOMD+pJaUI=", "s/UHxb+6kyO6vVgZ+bMiI3ZZhDaY2ZFKl2mAjrfYcKI=", "F9XppsJFDnZ/PgqSqLXuQhqT5MPRDVRQfu1a5U3zO+Q=", "attWzhQEA7UVlXQubW7rmC334IkoNy3WDWxiqiNYhmA=", "tP029Mz/uqENK41ROUuw3WceD/iaWBcGVYJmULpc+MY=", "sdUhnvJtX70WVhLK1gacoQsSdumyO4Orh2NWc/SZycU=", "WAwNpoW5v8DLJJh1DJcntKJTmCHP2hoTRpy7iOFguKw=", "vcMNg8nNWyJdJIPibKIN5tfY7Kurmv6ff9ZYRbnPxSM=", "i+1bXyPuPFkDeMlEH6wkckQgBtxyuuiawDAXX9rCmSc=", "glvCF62IcEyuARmNAQl0QmzfYft3G7m/tDB69fYNPXw=", "y55SjTyFs61jGR3EMxev6Y7DVPbjoOErLyUp9VOFzQI=", "B9jtHZ4aE9sJmR7+x3dGbDnG3543mrcFVDvf/UCu3I0=", "mpTJik3Aofq3JW0R0czQ1YXTNDyuVh//58IUyBjw1DA=", "soSnvBMHQo8bzQlcHZZyyxFgjq5La1AJs+ndt09GdEc=", "lT4RZAzoUuQadO/gFGYh0ih4IgVLWFJMhX692iNU2zQ=", "B3FT0IoFflGsIlbw5rqJkn0DDDoHxQTdI7BdaCIGIoA=", "Wiz02uCyQIaXrU5eOIn+yqXLjS80A1m/J+oOYlOK7Ek=", "0DrpQpZhD/GZJYPJRj8u6mMRE+UcWPYQuSvXDxa0Mnc=", "cm0ovb7z7eawVmWJgJawdkxfNH/HU276jRLNHJwqAZI=", "sCCJJqmODAWC5Kuo+kCabMRmT327+8nude/XRBypgVE=", "0BPuA5Nwv4s5VRz1hzc9PcfA5r7fTV/8F+9OrsNLD6I=", "03xppT+kB5IWFow0uwquTJNoBamv+rSVES5AefQH6ks=", "QmKygnHHeRTyNAqNCD+i8UEitykdzA/om7Q3E6d1EDE=", "IRabvU9Itxn+ZmFJlKofOXAXD34Nh2pWQUFSwYivaUc=", "TbCXIiS4nWEtql3YQtOf8pQNTy+R9/KG4j5O/+yzj+A=", "qE1Ott0YToCkmw5INy8kk3fT2lFSqD38c+0NVzwhWxk=", "IcVOy3rCc/HgD5+qRtwcbGZyPwRjM569hxtbwlo/Zew=", "KxU2aOtHM8jXtWlFeBKlR/Vq/QCkrRngHyCi1gglaBA=", "OfrmazM11JZb0H2Zl34mvmQlG9J8iRE/ToAAg170DE8=", "AMbjrNy5tUSmCp0TJ5akecwT2Zoa2mqgNTlAYibbUeI=", "o+LS2Kf3Cbn25okCZ5bB2tmeJWV5XiSfYLgQiGtXvIU=", "4b5u4dBTQEqIQ/3V4irUY2S+EFcKTyPBXbER19ZCIXA=", "HS43S11yTBtKWvFMlJYzX5Xp8FAf8hIPpNPif3xoFzA=", "rslMnwkTl5K0UPAeAxoaDRPkzyM+Kt5b6AGggMucROM=", "1NSfWbbqiMKsYm9R5fU2f2CtPUH3HmyarFNyeaBut4c=", "BOsM5F4aUHaja5voz+jjp2ragbDcD+wswjyzngeKpg0=", "ACzTtx9S07ymUMd+/N8IDOQJ3JoTXSVz21WAfJUzsV0=", "tSa8k7Ih65IexyFR/XT56pNtRn1DHmdWGN6L4CX0+kc=", "cAlga8dCkofxMyR9My1/3W2S7D4gjWSYHt2fFhHwRh0=", "VuhyrXlORLTutvUKxQVrx2XeqeYqhjNhN5z/Xm5/+Ys=", "V2rH94T1mCWC/dpH9rc20uTp3+GL5pwAmtYkz5vks6w=", "/xMsth9Hg1+J4NWEgX6gtMzU3lWD1Q+r2oM57sVfmJo=", "lFcX/yYaNwp/cysoHt6UcXXfK/vafalXfwBmOIbXJ0M=", "5SzrE4yiIZPcGeoVWXgQg5Ulr8Xw6lYScBuEBry+rg8=", "e0D+Ru6Pf3Hp7ValTARqhoRTE6/Jt62Xw3MgLehnTTM=", "Cv+tstuXvfaIPlsOjRY3qBuB2UWBztVDR3UaJuIPuv0=", "T+c659ufg+71p3ZxETBBJesEAJ+D6NIjGaQAD+Py9nY=", "scEtxzPvTW1qqtnxaD9tepLQHHNC6HCY+Gsg5VxNd7M=", "/fTNbirwSAAuTIicAlr8NfidJdxMor32HVgy29RuMWI=", "PqzO+kmjeRlMDpDVMCCbdy27mfiQD6ObsWT6yIKLkzQ=", "UqGhyIUkvqyQIc6EKzIVq7bJ4+Fm6Tymov+XDJ5pn4U=", "ExFQm1AFPRu/opJJQjOEVYzI9I2kM/6h9TJmuQDeyAw=", "azc4EKm2P1MeDBO3d1gimcCQGsvgezUlv80Ftz6uSkA=", "kGw1x/eW70oWjBTTY7pXROP9PheGJ95R5Rn8t2ZfC2w=", "VjsOs76nXcHWZdgAn6RVtXaGqskSKv5QuSA8h0P9CSs=", "MmkQSMHpQurEii9Pnl7qZxjlirNwr/QsVY4NpROrth0=", "X/rLoSOM5K0dVOk3mQPHUYVPZKLaV8KfQtWpz3IKreM=", "WvzUfOQ7jX+0GY5hQZsst/VbBLkvzlFiHKdUTdb5e4c=", "8dHjU8XDak3CD+uIy6nZ1QP9pOJuDwP4I1MdDRvgewI=", "UMmWHxQ4WyjnmVfjFL1aLWY80wpZ1dbJE1qeAV94hFo=", "bvydFGwQ/XzvfKuT0+msCYFsKyF53PBFaDkkmQjtVYA=", "xxNwZhXOtIbb5oKPfpA9NBD/CJd1Z7HBThIlV/2HNyY=", "caw1MjCLA9+s8yha16liyqLR6lF+gOBlrtitjRzdBvU=", "mHmWGSstoQ6E/Q1UqzjCwmdodAS86uQ4do8+dwQYCkk=", "7+avK1J1fGsU12q0vMrTSzOFymIywjkohOjq3Ft6m48=", "DPzb0W3VHaU2oSKBlpw1ZTmIkn5IxTJZnUvFYt4XINo=", "vMnFXbvO3S6wVJG3tkAf0XEq7N5Z3agIm9El5yaIVQ4=", "DgERrqapI+n/CR9mqOYaYbuqUVmFF1HXbC8bR+w/O6Q=", "HNkjbXvdlS2HLJ6wBS8IROQjcaRahWGN2ky8iwIoyqw=", "XGOKyAz/J0UuZM2DYWykPzczhjKMxoCsMP6DsCZKXAk=", "163Qnhj5mcHYHCCt6ax6iEPVMokWSipIx5D8hLO1atc=", "abRQvOKXO1/ZJucaBLPvisPQIkysLAfQj+uMAKSqWxI=", "wi/xaxGS8YzyCZ7IsNsYvToMJixRGw5OSwTvXCA4liY=", "Oci9ZRpiVuI7x/V3XZvCVpAFOtrkeDjTnZhkzq5bxDQ=", "Su3hkYxkW0GBQE+1BBAoaNJCc4fyCcVxPR1q3J3WURY=", "GxOY2kItPlncHlvMbw8VGLDEAsDh66esRWu6vx3uEpM=", "qAC+w3Z/qQPtk2Rvor+C/s5PGWPUbgLV6fST/+VDHzA=", "yyFHVXRnzrj9+kC929PIkmhR5LLcEb6zEQaGbNgs/xQ=", "QGfLkD10ARwp2A/SXomZppXn8tZMwFe4lkirfDCf/yI=", "SeOlP6b8RmQhX7W79sdKv9S1MZx0W3DHhU0aInr28ms=", "Hrafo2dKvnHXrnIjSsLArrsWQchm73Wn2Y2Gpg7rAUg=", "oRg2fyBgeHfWIROgi+HiGgeBqfqYkmyMHyBp5d9auNY=", "u3hn+HDtjlwlz2KQ/taaONS8z3yWpO8inZT94WZRmDw=", "WVpqmPkhn8RnuJDs2gJLCa0TKrpWpqQeVHPMugOjcqU=", "TzcIcLd3tyIZmvkQr+D04DImtYJVRaNdcINbMDWt1cc=", "bDEexz/1BFUiSu0eZtD2nWF63EooYENtYIU06JbmC2g=", "Zsn1exkMVs1yXaxWp0RlG/hXx1M+7LEIQtdfTjor+9c=", "U0VrQn0gBjYr9VQBGbL6DintdA0DXfs8Dzt2b3BuAS0=", "COuPtrZC6UO5y6axCDqfH/u1KS9MVRrg3sENol7qzR8=", "VTNlcoXzRiMUGhKii7hOD+GBRUEfPQd/W6FSnlm3CdA="], "CachedAssets": {"Yp6Na6OHuAM9319B7rpHlJcqMSiWcANGtD8qC1WRyzA=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\g9w5tdrjvt-i2g872140o.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "bootstrap.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.37\\staticwebassets\\bootstrap.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cr8br0eb2j", "Integrity": "6GN2r+r1CoSpfAzxQuuTZrKWmGTSotpCr+RDdnVTB1c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.37\\staticwebassets\\bootstrap.css", "FileLength": 476009, "LastWriteTime": "2025-06-28T04:51:41.3634699+00:00"}, "jpyHQsU+D+mgb2Y6mJ9lGxCiXyze56dQ/VyKxro3uCE=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\ev84ybuhfb-c40fyl3ara.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "bootstrap-dark.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.37\\staticwebassets\\bootstrap-dark.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "92frdq3qn9", "Integrity": "//U/mCqxk1jFjtIW7Gf+AtpzcxhzUpqverMS3Myr/P8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.37\\staticwebassets\\bootstrap-dark.css", "FileLength": 475446, "LastWriteTime": "2025-06-28T04:51:41.6380074+00:00"}, "/hgI/5RVDv+IUV4tWah6nq/nnqOla7MN0//mIq0bB3M=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\ll0ck2c3pw-iz9gyka5i9.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "bootstrap-dark-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.37\\staticwebassets\\bootstrap-dark-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rjm2w9wowk", "Integrity": "WT3SkLlQxIvr+I4F9iakIgKx3+GPihj+HVKipaSlVXc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.37\\staticwebassets\\bootstrap-dark-lite.css", "FileLength": 475449, "LastWriteTime": "2025-06-28T04:51:41.7320056+00:00"}, "VOuIqqxjjYkoYkWhL/9qYmXwjAwxnZpVbPKl//VNCW8=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\m9pq0qobmp-5q9lwcsdf0.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "bootstrap-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.37\\staticwebassets\\bootstrap-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "23f1jiu1cz", "Integrity": "np2B+Xp/zpFTYiHoRhLfDzXWSjulqv4He0p6gIP1Kpw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.37\\staticwebassets\\bootstrap-lite.css", "FileLength": 476017, "LastWriteTime": "2025-06-28T04:51:42.1754086+00:00"}, "3wW2VJb8JZUBN+uPgAW4E5lXIa09NLk1S/UFPCaOv8Q=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\g0kkhqlx3b-gz1e1z4pqv.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "bootstrap4.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.37\\staticwebassets\\bootstrap4.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xorkwtx4al", "Integrity": "MY3fNazEEZMNsVE84IXPNR98FHHQlh48ME/jVjwlT2U=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.37\\staticwebassets\\bootstrap4.css", "FileLength": 466954, "LastWriteTime": "2025-06-28T04:51:42.7235766+00:00"}, "MF0lXqoMz5irgbSEBh6oabdRX/cqhzZaotObo7VcewE=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\fx6f6w99v8-jjs54pfh5m.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "bootstrap4-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.37\\staticwebassets\\bootstrap4-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "m7f2di7yvr", "Integrity": "gl43XETDODIpxO+UoPMfSEmkT9uL7LBvfT5HCasnhg0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.37\\staticwebassets\\bootstrap4-lite.css", "FileLength": 466959, "LastWriteTime": "2025-06-28T04:51:41.8085841+00:00"}, "6sG5RM4C8xsLV5b9jpbEk7B4pyiiFpqt+CuLrwoRVCw=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\czad4b1t5w-qhde89oftt.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "bootstrap5.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.37\\staticwebassets\\bootstrap5.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ee0jcom333", "Integrity": "xpQVOckGkwkB9Ob2OrERYeEtxW2jVC827o4VNhsi1Nk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.37\\staticwebassets\\bootstrap5.css", "FileLength": 474304, "LastWriteTime": "2025-06-28T04:51:42.6282171+00:00"}, "Abin/FezBWpSH//xK9nO77KfFTNr7VwkfItDnf+PPU4=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\w7wzav3zkk-v83pxlq744.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "bootstrap5-dark.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.37\\staticwebassets\\bootstrap5-dark.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "gd7nxdizls", "Integrity": "FHpvvIOqTIAnmhKhokfNTD1cHJHxPaFIWlRI/I2/PuQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.37\\staticwebassets\\bootstrap5-dark.css", "FileLength": 478165, "LastWriteTime": "2025-06-28T04:51:43.0646762+00:00"}, "2t9pFxkJnjxn3Mk6/xB1xfb22aPbRL9ztVm9TmJdN8w=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\ezik3was9u-sdz3396v9j.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "bootstrap5-dark-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.37\\staticwebassets\\bootstrap5-dark-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jsuh7lm44b", "Integrity": "3Q7s2NJa8QXJ70qJe/+J9BUXT6ckgggvS9UpdrYiv2o=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.37\\staticwebassets\\bootstrap5-dark-lite.css", "FileLength": 478171, "LastWriteTime": "2025-06-28T04:51:43.333766+00:00"}, "q2J1bmrHsKwB/y+IcQzO9NQJBAqAL2wLrpH57OuuawQ=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\0gmeqlood7-7blbp1mce4.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "bootstrap5-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.37\\staticwebassets\\bootstrap5-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fdt1ip4895", "Integrity": "8+AzyicdoNZiJUdt6YKbjnbYC72UsDzSJq7Gz59bjR8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.37\\staticwebassets\\bootstrap5-lite.css", "FileLength": 474310, "LastWriteTime": "2025-06-28T04:51:43.7223014+00:00"}, "nhTwtmBufG1mx5ZAZb0lb9nR0tdNYjWxjFpw0zvFSis=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\rceg06wfiw-wv5ulmhyte.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "bootstrap5.3.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.37\\staticwebassets\\bootstrap5.3.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8yaz4475yw", "Integrity": "MOb8OA91I1d6dIq71XKnmRRijSoUr0IBZlHRwRa4nBQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.37\\staticwebassets\\bootstrap5.3.css", "FileLength": 479990, "LastWriteTime": "2025-06-28T04:51:44.2192167+00:00"}, "osZpFnH60p+UWBtGNDbF6aIE5DbkFX0zUVhoOLcF64I=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\5q2prff4m2-kebdmujib9.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "bootstrap5.3-dark.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.37\\staticwebassets\\bootstrap5.3-dark.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9i0telrpk5", "Integrity": "LyZiKqE1ElPWgfLmiioT/pUNTsQZGUXIhR8vY6PCw1M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.37\\staticwebassets\\bootstrap5.3-dark.css", "FileLength": 479086, "LastWriteTime": "2025-06-28T04:51:44.4878166+00:00"}, "JlN9hzWDpaRinmOZpI9boHuRcZgU4NV1H8UQF1YQ+as=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\awwokdaaq0-xk8az7f4fs.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "bootstrap5.3-dark-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.37\\staticwebassets\\bootstrap5.3-dark-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2sz6demyzp", "Integrity": "XgCywPS4/zXpBCVHdhjpvTX4C7nG0m5V7f4exErbbSM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.37\\staticwebassets\\bootstrap5.3-dark-lite.css", "FileLength": 479088, "LastWriteTime": "2025-06-28T04:51:45.0030576+00:00"}, "JH+d5oIlexS5TXCNTmEFIle1kwW/6Q6CTyS+cXzpfks=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\984b865z1b-ef581g9zll.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "bootstrap5.3-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.37\\staticwebassets\\bootstrap5.3-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ucja2f4j5p", "Integrity": "fl5Oaq4iiyU9arLEkx2bp/JMApwb/vBl1Mk4LhKV+Hw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.37\\staticwebassets\\bootstrap5.3-lite.css", "FileLength": 479995, "LastWriteTime": "2025-06-28T04:51:45.2172893+00:00"}, "4ZtSa42/cMU5SDIL6EVZCRcmckY+lEfbtPYrS841P3Y=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\ytu8lus5q2-6zshlbbixp.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "customized/material.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.37\\staticwebassets\\customized\\material.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "6ru5la4yh6", "Integrity": "8ofipkhbDN0jzlwJYvtNIl4wzLm7N64bgA2iGunMk20=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.37\\staticwebassets\\customized\\material.css", "FileLength": 503997, "LastWriteTime": "2025-06-28T04:51:45.4316806+00:00"}, "j95nYS82epFhJ9MpquHCVnW/Q6S/m1aYVJOq5uPfoEQ=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\mpbuet7hpe-umulwh652s.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "customized/material-dark.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.37\\staticwebassets\\customized\\material-dark.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "a9sgesectl", "Integrity": "DpFrjbk8f69zGnHSjiDSnxmTXiAT2KUgco1v9PfRNYw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.37\\staticwebassets\\customized\\material-dark.css", "FileLength": 510474, "LastWriteTime": "2025-06-28T04:51:45.6781733+00:00"}, "Tnf/Gvgd8WOsx9EXYRlb88JTmgCB2Ize3P0saiDHP/s=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\pxx57cfmdu-zm9rzsb9zl.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "customized/tailwind.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.37\\staticwebassets\\customized\\tailwind.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kiiaayfoae", "Integrity": "PH4beuGJV6dB5n8kaJphTGZXYRwmZGGsnQFuhztcICM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.37\\staticwebassets\\customized\\tailwind.css", "FileLength": 438439, "LastWriteTime": "2025-06-28T04:51:45.8573947+00:00"}, "HMxWN6mYG3Z57y3VDJvxaySm94m5V8sM3pp28SrKHr4=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\yu10ux9zg2-ozbnyg3t5p.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "customized/tailwind-dark.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.37\\staticwebassets\\customized\\tailwind-dark.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "c4cw2z891r", "Integrity": "pIxr3K2iM3ml7uGnZuguyGDMDvQnjfcZmZmBRft2/vs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.37\\staticwebassets\\customized\\tailwind-dark.css", "FileLength": 441420, "LastWriteTime": "2025-06-28T04:51:46.0152201+00:00"}, "5jqjA979Y4obmpgj4SLZPprrgyYt6wLnNEP9NsZgxQo=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\xj9e5awasb-5kxn8q7zaa.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "fabric.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.37\\staticwebassets\\fabric.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "gmq3a4al51", "Integrity": "iGN4ha+nSU4x586zydJZn7zfue3q0k6qeQFQjllobOM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.37\\staticwebassets\\fabric.css", "FileLength": 455482, "LastWriteTime": "2025-06-28T04:51:46.249568+00:00"}, "LhfTRnjJM/+FTW8TZk3/ycCGFsV1hn/NcSofqZLzmgc=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\f1vaqyeoju-yumv9blev4.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "fabric-dark.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.37\\staticwebassets\\fabric-dark.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "f2eipxrasw", "Integrity": "tyTeQEjP5PY7YO4JSe+0pXZ8b3VkZXjDBpn9xSPOicY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.37\\staticwebassets\\fabric-dark.css", "FileLength": 465452, "LastWriteTime": "2025-06-28T04:51:46.6525192+00:00"}, "FtijI6V2GHh+MChHKpcymZlFVtsNEdnhJ533itSAcOY=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\8fhnih39yd-la93zhwvva.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "fabric-dark-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.37\\staticwebassets\\fabric-dark-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "55ih2czzeb", "Integrity": "56cENWYkP6TEUPH5GEkeBaZpDzk7SqVvm15XXFtsqJk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.37\\staticwebassets\\fabric-dark-lite.css", "FileLength": 465454, "LastWriteTime": "2025-06-28T04:51:46.824749+00:00"}, "wMEk3F7U3ynAO2m20pODiR+MI3JnGM5PjsipNz5ACmQ=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\aqas0gzsh0-xxwqt750y0.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "fabric-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.37\\staticwebassets\\fabric-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "oggjq9g3xm", "Integrity": "6EfjlTmMrzXQuENHOqzACHzaqbGqUkA3VyJcGFDWWcE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.37\\staticwebassets\\fabric-lite.css", "FileLength": 455490, "LastWriteTime": "2025-06-28T04:51:42.1258287+00:00"}, "ox+DUq2ZWBAa8dqLipXDobMv24LEMBUB9FlBMVUTWM8=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\r5ssla993m-8skt3q6tyb.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "fluent.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.37\\staticwebassets\\fluent.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "96do7xkfdx", "Integrity": "TxwIGJrTGFLA7PUL478yeTMpNawTubHcDffESQFLIyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.37\\staticwebassets\\fluent.css", "FileLength": 454429, "LastWriteTime": "2025-06-28T04:51:42.7035755+00:00"}, "AQd9ciffbqPuwKEBuj//cwtI2sN2GMDR1LGzSYhVd0Y=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\yqbsc2e4c2-q6t3tx666r.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "fluent-dark.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.37\\staticwebassets\\fluent-dark.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vc06i1ritc", "Integrity": "sZLvzkLBY9TewDrgdBIhhQzdGfYFKIqm1RRJNa2uP4E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.37\\staticwebassets\\fluent-dark.css", "FileLength": 454755, "LastWriteTime": "2025-06-28T04:51:43.2205869+00:00"}, "ntbKhxkR4+faRsGhm1RvugKOdy5KjgQI64ri+l0w5as=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\2dszv1c0dc-td9ae9u3xh.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "fluent-dark-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.37\\staticwebassets\\fluent-dark-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nv1xgrnqc3", "Integrity": "PSza2l6jrTXuUDkef1hQtPWMjT32RgdDW2DcdasAukc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.37\\staticwebassets\\fluent-dark-lite.css", "FileLength": 454757, "LastWriteTime": "2025-06-28T04:51:43.8093196+00:00"}, "y6uIJcHWm1J85rpHM4yW7eQv/Q6+GWRfsOj13VFnt7w=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\ubxng02trl-7bo1xv0y1t.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "fluent-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.37\\staticwebassets\\fluent-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nh58gr8sbk", "Integrity": "y6nB9QBppY8Jtppr+pq5YkQ+2qSj38eaiNuRGv4IDGw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.37\\staticwebassets\\fluent-lite.css", "FileLength": 454433, "LastWriteTime": "2025-06-28T04:51:44.243861+00:00"}, "kAmxIXaZIF0OavnpYU71LzX7jTk0pmcrWs7Y+JMa/0Q=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\h56puxhs03-jqriujue3n.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "fluent2.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.37\\staticwebassets\\fluent2.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "6b7x7ejfnc", "Integrity": "vg21+EIzglj5pNYs6R7f8WbQEhOCHKPuI7pJCEzKY7E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.37\\staticwebassets\\fluent2.css", "FileLength": 490074, "LastWriteTime": "2025-06-28T04:51:44.5257345+00:00"}, "Y6XufGouw4TSGucs0UWc/Z4SnuFLHLhemZNvv49vetQ=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\oaindoncb5-c6vvogfiwj.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "fluent2-dark.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.37\\staticwebassets\\fluent2-dark.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vu80d73sc8", "Integrity": "1wUgA4s/lu98zn8mQ2l3ZDC7JjCuc+jdjVW1zNAobfc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.37\\staticwebassets\\fluent2-dark.css", "FileLength": 487060, "LastWriteTime": "2025-06-28T04:51:45.0374236+00:00"}, "WiENPxED3c/FO0R1K2ifsYVDgK6woCUlrYl9Va8CYco=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\xhfo9ezgkd-m0rle8326f.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "fluent2-dark-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.37\\staticwebassets\\fluent2-dark-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "se1y25y25s", "Integrity": "pFHCZjG5VL8uE5mAYolz9Axu++DYadIDqG/mDt/DdZY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.37\\staticwebassets\\fluent2-dark-lite.css", "FileLength": 487063, "LastWriteTime": "2025-06-28T04:51:41.792584+00:00"}, "qLr+Xp+7aKC1XN6AOpQN+IvvURj50A31UI88PVTE7Eg=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\1e4urs263g-etsp7g833c.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "fluent2-highcontrast.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.37\\staticwebassets\\fluent2-highcontrast.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vqagb3qqux", "Integrity": "Z5FhRj/511I59A0X3D3Io0fMX/08tCaPEDlPUZJ4F3A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.37\\staticwebassets\\fluent2-highcontrast.css", "FileLength": 488579, "LastWriteTime": "2025-06-28T04:51:41.4014652+00:00"}, "hMcpn9jPwQHeorvUZZpOxt3Bqi0ClZKlE2yw/EdCPJ8=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\4qsv9tgae5-mrbdda3cj6.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "fluent2-highcontrast-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.37\\staticwebassets\\fluent2-highcontrast-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "q3vpmbj21m", "Integrity": "tvtO3Zjp0JVSEgp3W7NYWKLfQ+/2hsB/2qYuIjZx+ig=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.37\\staticwebassets\\fluent2-highcontrast-lite.css", "FileLength": 488580, "LastWriteTime": "2025-06-28T04:51:41.3804651+00:00"}, "bVrG2xP2SZIQzfOWSBnNvhPlWO/G9Ey2ruoE4n3YFN8=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\lf21o06s6y-c4l8bzzz3d.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "fluent2-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.37\\staticwebassets\\fluent2-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "grqlezqx8x", "Integrity": "RGsrSCPvviyfUufIXf20pn6r1vlAjrKCWMwLsaabAEc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.37\\staticwebassets\\fluent2-lite.css", "FileLength": 490080, "LastWriteTime": "2025-06-28T04:51:41.8125847+00:00"}, "pmgWx8k5qjo/HJszze9yafqSWah8440ubYt+Q2q8D04=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\8u73xo5u52-qfv16a8w3p.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "highcontrast.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.37\\staticwebassets\\highcontrast.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "wo2nm1ou28", "Integrity": "oBhqSLOycK6e50r2qOmcL9LWr9m9JLMXgx0LxRWD+qA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.37\\staticwebassets\\highcontrast.css", "FileLength": 453411, "LastWriteTime": "2025-06-28T04:51:42.5909071+00:00"}, "/8QG+N6sA7gQ9CQxTjqpOoJ/hgNsTeoVztauvYhwQsM=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\sa1475ed2m-i1y08pb6r2.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "highcontrast-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.37\\staticwebassets\\highcontrast-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bgb1d6cqxa", "Integrity": "OzXOe6TWZR9EfNC7M3fNCrfnh/DDz5LiHPTwIJvzzAQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.37\\staticwebassets\\highcontrast-lite.css", "FileLength": 453412, "LastWriteTime": "2025-06-28T04:51:41.7380063+00:00"}, "icwaKbSciJw6JjZ+hUPFhnPKfjAEbEXQo3jE5Vc6Pvw=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\w0orhpiaxo-ne2q2zvmee.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "material.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.37\\staticwebassets\\material.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ihh7b6isaw", "Integrity": "usi4iVvo+UDOxLeNtlB/8xfUEfmQALwTLNxp/RAndr0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.37\\staticwebassets\\material.css", "FileLength": 504051, "LastWriteTime": "2025-06-28T04:51:42.3943916+00:00"}, "nOo0gfiElFRwW7fPUQqDgOuwTIT2PQtxwg8GEaEdmvg=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\4ulei2vwud-qgqgkp4l86.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "material-dark.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.37\\staticwebassets\\material-dark.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "os3d34y14m", "Integrity": "iNj0e+9oqk2otyvmmxrMCzDo/qmhSKNtL71RA2y83vE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.37\\staticwebassets\\material-dark.css", "FileLength": 510502, "LastWriteTime": "2025-06-28T04:51:42.4193764+00:00"}, "iCmdtj6TU3Z7vpm5qpA5gCYWUrmY5IdPNa28vuNDpRY=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\ay4l9je90m-ejzuq06h0v.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "material-dark-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.37\\staticwebassets\\material-dark-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "tm8db9nnj2", "Integrity": "9UsgcFLNrm087HIQMl6oUb2Wzyn8cgE4Cw7F3qSVm5w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.37\\staticwebassets\\material-dark-lite.css", "FileLength": 510505, "LastWriteTime": "2025-06-28T04:51:43.0376742+00:00"}, "X8tXR9vKH9w9nRLFdzzA9ZYxbtWtt5QcP6BJ1S+J94g=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\fsp2o9negp-l25dbew49m.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "material-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.37\\staticwebassets\\material-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0tcovrvx8h", "Integrity": "LK7/jJjdg9yqWM07oe+eTDL688trCyNLnf60aWJk/CQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.37\\staticwebassets\\material-lite.css", "FileLength": 504057, "LastWriteTime": "2025-06-28T04:51:43.2649235+00:00"}, "Z66HuAnGXrOfnEf62jS9v3YcEUp/fSWk5C63Pva/Owo=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\s2yf70i8pu-rxlh8xcfrr.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "material3.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.37\\staticwebassets\\material3.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5ecrhboqdu", "Integrity": "fTR9VgzzdZViHzMInBzTTyfF0t0iz6XT3EGj9JdACUk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.37\\staticwebassets\\material3.css", "FileLength": 455966, "LastWriteTime": "2025-06-28T04:51:43.7812073+00:00"}, "FKF9COdQxPJ4DRQMbkqmrAnrrvWb/6gMpjCAnWqK2Mk=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\wa7m2rhtxr-58b04lxeb2.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "material3-dark.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.37\\staticwebassets\\material3-dark.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3rx14l99vh", "Integrity": "HykCI0F865t66nv5XpYo3imUm+Ui5xxgMQCrhdPbRv4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.37\\staticwebassets\\material3-dark.css", "FileLength": 455511, "LastWriteTime": "2025-06-28T04:51:44.230863+00:00"}, "C1PGKvh+Hg1aIWoJJ0IFr+TrbLADSiXpwWC947TRWUg=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\yjifzcnj40-j7l0bwkkcm.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "material3-dark-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.37\\staticwebassets\\material3-dark-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bcxttaaq2u", "Integrity": "9ewVOIdEAGrprPea7wzrIePQMoSPU5gyXaSaHKXDt/s=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.37\\staticwebassets\\material3-dark-lite.css", "FileLength": 455515, "LastWriteTime": "2025-06-28T04:51:44.4878166+00:00"}, "80uJYTbthenKH7RBkTP7r8AThBF6FJ96cYqs9xq50bI=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\wkxfmls346-w2jjzrl3tv.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "material3-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.37\\staticwebassets\\material3-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3olz3qewzn", "Integrity": "c8MkcVS6dpkI3dEQ6ob1ghIoB7Amn659XggHs7r8SOc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.37\\staticwebassets\\material3-lite.css", "FileLength": 455974, "LastWriteTime": "2025-06-28T04:51:44.7922567+00:00"}, "umfhERbKIWQhJa9s5P9gHdnTGhDHP864yUY/XU5zZOg=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\h5th63hdh4-1q00rsok8r.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "tailwind.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.37\\staticwebassets\\tailwind.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "78poa2ihde", "Integrity": "t4c9qnz0hjZmCiDjSE/j9W+w8xL+Rpr0AL1Kpe/SrCI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.37\\staticwebassets\\tailwind.css", "FileLength": 438513, "LastWriteTime": "2025-06-28T04:51:45.1664212+00:00"}, "NOC1+JrTC+Wea4NcQQ6PlhDylbd/6YtOIJpfIU24HB0=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\sxqxspocya-1avtkruox7.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "tailwind-dark.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.37\\staticwebassets\\tailwind-dark.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fzxjdfne1n", "Integrity": "y+vppyoqsQFY4LZoSJ29QJsGrki5yXhSXcG3ZDNQuPo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.37\\staticwebassets\\tailwind-dark.css", "FileLength": 441497, "LastWriteTime": "2025-06-28T04:51:45.3357277+00:00"}, "NqCMNCTmYGBnhF0xbMAAAhD5PN5VDf9A2UftaEjpkNw=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\rd6io8kvbb-03qr4t1egm.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "tailwind-dark-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.37\\staticwebassets\\tailwind-dark-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "szeohfo4wh", "Integrity": "6C57jPJHITIJmv18U/U2ht+ydWQeG9R7R2pgGXcuPbY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.37\\staticwebassets\\tailwind-dark-lite.css", "FileLength": 441505, "LastWriteTime": "2025-06-28T04:51:45.516907+00:00"}, "XKNuml1hCJ5rz+5sAtL3WhgeKSoXY3g6iIkg/W2+RH0=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\g3fd5cikf4-m2qpowulgv.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "tailwind-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.37\\staticwebassets\\tailwind-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "g82g2g8fy6", "Integrity": "sM7+4E6xteqKJtffrATgA816G0R/omjwwyej72nj1Wg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.37\\staticwebassets\\tailwind-lite.css", "FileLength": 438515, "LastWriteTime": "2025-06-28T04:51:45.6923565+00:00"}, "3L2Hkf38q+OSeG4DKB7OSVcSfZYQOA+eDzbIrrIUnCU=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\k08v6vvvwj-75jc18zxh3.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "tailwind3.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.37\\staticwebassets\\tailwind3.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cxixg105kq", "Integrity": "ENKfiv5++Bg6ZKcEOD7UkhIa3UnF62uDTk8AoCW3WCw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.37\\staticwebassets\\tailwind3.css", "FileLength": 498779, "LastWriteTime": "2025-06-28T04:51:45.9056157+00:00"}, "GkDCQzccyk0RxmkfViQ61kgulmouwT5axzv5WISBvUY=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\npcbabgmoa-e19qfwtwrl.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "tailwind3-dark.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.37\\staticwebassets\\tailwind3-dark.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "o08tb8wvof", "Integrity": "KGfsTMPK5gO/+zMj0BD1hHhi0RvFXfSPqjsI380LNbg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.37\\staticwebassets\\tailwind3-dark.css", "FileLength": 496648, "LastWriteTime": "2025-06-28T04:51:46.1013057+00:00"}, "9xqJg0r4l+WtqLd6N+rncpNF4cHw5n71lcZ7WZoX3iE=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\4cc4cl8qb1-32k2zs5bmi.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "tailwind3-dark-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.37\\staticwebassets\\tailwind3-dark-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "14i1oska<PERSON>", "Integrity": "8dmuxFBHkRYfOwon7Vh3qEon50Q18WE7/96KaZRc/dg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.37\\staticwebassets\\tailwind3-dark-lite.css", "FileLength": 496651, "LastWriteTime": "2025-06-28T04:51:46.612654+00:00"}, "q+rPxkxhTda2It40OPqpq1VEhIEvgKbOFTPKvun+KCo=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\3itjbw64bu-kuihdgzoxs.gz", "SourceId": "Syncfusion.Blazor.Themes", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor.Themes", "RelativePath": "tailwind3-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.37\\staticwebassets\\tailwind3-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hg8v4w9558", "Integrity": "kRDlRgL6b7QNJA9cGOaaaFudcIakrEElACyowUVSTv4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor.themes\\30.1.37\\staticwebassets\\tailwind3-lite.css", "FileLength": 498782, "LastWriteTime": "2025-06-28T04:51:46.817461+00:00"}, "Vg2/DyssOuQ7UiLgnn1FSr9qwjqsIphTgQj8S82N0ac=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\2tby22c7r1-lbmfub4x2c.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/data.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\data.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5lq09xg615", "Integrity": "ZyxeajUPPZneNfnc/64LZ5bvt0fZIEBvlqHK4weTcJg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\data.min.js", "FileLength": 24740, "LastWriteTime": "2025-06-28T04:51:46.8264414+00:00"}, "MHMkhr3He4PXsip8LKAP/kXBkvCzs6CLudYVS8qvloA=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\ddg9dsjctj-tz7qeeb3lo.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/navigationsbase.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\navigationsbase.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nlic41vr1t", "Integrity": "1T49X7JJ3cfxl5b7mBQnowF3MftDE9jIIyO2CLAp0kw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\navigationsbase.min.js", "FileLength": 3974, "LastWriteTime": "2025-06-28T04:51:41.7140085+00:00"}, "ygDIehyDAlySsDhWozKT0ds40y8+aqh+/KlcjsjXwoA=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\s0a6p2lsn4-e21v6zo5rh.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/popup.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\popup.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ecwtysjdzk", "Integrity": "AvVOOZ4gmkSiJ706FQsl9A+Y8B2L/WIc4O+Yi1Zg5UU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\popup.min.js", "FileLength": 4244, "LastWriteTime": "2025-06-28T04:51:41.7895838+00:00"}, "7BzyxV11vX/HTXBvQrPbbEyQMP+PrfGdfPPx8O0W5cE=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\bmz5nvmby1-io6jyjpz2x.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/popupsbase.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\popupsbase.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ngpchzx5hx", "Integrity": "53jOHc5D6fZHple8iQHr/Bnj1HANBz45CMiV5Nt1Ork=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\popupsbase.min.js", "FileLength": 4426, "LastWriteTime": "2025-06-28T04:51:41.741007+00:00"}, "eAKEUeQxo4P2OY4Yo0E3xb/2NsAAygGpcxjHvr4Eki8=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\ut6zzcs2sj-xwxfj3k7z8.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-accordion.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-accordion.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bymf39t4fa", "Integrity": "DWcDCLiCoPqxK5aEl1kOk9+eatAg6WEiJiOmstiOAE4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-accordion.min.js", "FileLength": 3455, "LastWriteTime": "2025-06-28T04:51:41.7516994+00:00"}, "u/UvCTQgASUpF7+0VRX94+gBBaH8IwxG8c+BL/Gbmgg=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\xhreyxaaqf-4up56tt8qm.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-accumulation-chart.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-accumulation-chart.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2j849r366w", "Integrity": "0lbevtccOur9fwIS3O/vXLFdKXrZZtXPY7Jb+7dIDXc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-accumulation-chart.min.js", "FileLength": 6149, "LastWriteTime": "2025-06-28T04:51:41.7895838+00:00"}, "HXpWftpHD1l3KGIEQTYeaAwi7So0lNOvlcl24NR6aGI=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\d405vrip82-t5u4b7dng2.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-ai-assistview.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-ai-assistview.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "eqasnrj3nn", "Integrity": "qidzrZiRiXmKCg0DQp4sWMaeMXTWmfYn3GZ07kSGnJI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-ai-assistview.min.js", "FileLength": 3460, "LastWriteTime": "2025-06-28T04:51:41.0100855+00:00"}, "A6JGlNVixLQOyu5jJjgd4t1lQNRuyNCowxH1/m2Tb70=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\in9q5p7h0u-8ocdp0f9lz.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-barcode.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-barcode.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2iat723pgs", "Integrity": "avmeT/5jcodBa3vCFppXk29uYmyJ77KsStjjWv43GGE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-barcode.min.js", "FileLength": 2546, "LastWriteTime": "2025-06-28T04:51:41.0390837+00:00"}, "29ape4XKJkZZKEd/PLfca/8RymAbRtqedtwnE1pcubc=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\0f3xp4yca7-46r5eq2tm7.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-breadcrumb.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-breadcrumb.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mw2zl2vbx9", "Integrity": "itmioFEI/gGGgHozXokFqD7DI4TaI6a4wxsyD5FobGA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-breadcrumb.min.js", "FileLength": 1787, "LastWriteTime": "2025-06-28T04:51:41.0130904+00:00"}, "/01MpjBIBlT64Iuwtb7LPSBJaH4sE9B2jK9l7e7RIQo=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\9oodxfd7jy-9k5ge7f3fe.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-bullet-chart.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-bullet-chart.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "x4e1h0krer", "Integrity": "0a4c7Y9QEfR68FkYwaBuR6OuPxEFo2aZbgZeVYiYxUY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-bullet-chart.min.js", "FileLength": 2440, "LastWriteTime": "2025-06-28T04:51:41.017089+00:00"}, "crG3HCNKM91KSNyW86Rd6/SwYvH7n5PvIFHJbOTvqSU=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\iyvfbpj4yj-8y1zw46xlt.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-calendar.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-calendar.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "np2ryo185m", "Integrity": "hefnBOw6IxS8B7OT2S8UjzF/sM/ZtnyYUNwk77lpbS0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-calendar.min.js", "FileLength": 1014, "LastWriteTime": "2025-06-28T04:51:41.0202084+00:00"}, "gUSXYPO0siJcHxtfFCsn55XY6ahDZhe11wejD/JtJSc=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\44mfw27k7i-gyd1mu11g9.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-carousel.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-carousel.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xz2xtqq6rg", "Integrity": "k20kULGYc2HPUG2YpISdnQTzf8LdlgVrzvkjpSS9dUE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-carousel.min.js", "FileLength": 2140, "LastWriteTime": "2025-06-28T04:51:41.0217506+00:00"}, "+PaZeDLRCJ4mATxVENPYETAg27Q0jkan2gwJtqpz3JQ=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\cfvyxuh1qt-kcupupl319.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-chart.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-chart.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "d6xbschlz3", "Integrity": "S+fvoeGindLK+8jGANgZskcDUUvX356yizFtU6QK2Nk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-chart.min.js", "FileLength": 54271, "LastWriteTime": "2025-06-28T04:51:41.0853515+00:00"}, "dvoGju/xFXowvL1Ddsx4fm/ZXq1giq5qbqgA9f1bE/I=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\wye317dvcg-xx9v8p3ne1.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-chart3D.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-chart3D.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p71s9v1fqh", "Integrity": "JUrHASSS4i9m0sJNZ4trti4AxUpLY4VZNrNf7rbK/hw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-chart3D.min.js", "FileLength": 15883, "LastWriteTime": "2025-06-28T04:51:41.104885+00:00"}, "S8lBV6xXJmg6SyfRgvZ/eS4kWZ5SvrwR8NSM/zLOmG4=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\1ekojsgjxz-cwfaudh4go.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-chat-ui.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-chat-ui.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "n00oqnepas", "Integrity": "6bOOy5R93d/xsPaTswJ9hs3l4IkUv6/C6jn+YfpQbRw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-chat-ui.min.js", "FileLength": 3725, "LastWriteTime": "2025-06-28T04:51:41.0494866+00:00"}, "oPnypIjT7psBuADWWvQvROl8AaT6YXqWoG/ipXsYF1o=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\y6tbkm7tzg-6k8x8x3nvc.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-circulargauge.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-circulargauge.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "39vblc15f5", "Integrity": "CVi0A+sNIsbzItlLTXst2BFGGPhkhEFc37D/pmfLfH4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-circulargauge.min.js", "FileLength": 6777, "LastWriteTime": "2025-06-28T04:51:41.0853515+00:00"}, "ZWkTc+PKPvZjVMEmqipsfRW1wKG3t8yNaMgtX8p24Sw=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\elmu9o8lon-pzfa0f2xo3.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-colorpicker.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-colorpicker.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yppomnifhf", "Integrity": "NgoccPzD/7BxAqF2ouyISgWm+/n8fwKgFAyUZkTuX5A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-colorpicker.min.js", "FileLength": 2153, "LastWriteTime": "2025-06-28T04:51:41.0968859+00:00"}, "MaBKL184A8qRn/YXtONfAA47DMYhWTyG4GCPCcbwNrg=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\rwu2eraxoe-z73iuhcsrx.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-contextmenu.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-contextmenu.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "as9k5h13si", "Integrity": "drheGAJj3tWvE/0ycMMtnW4YsIi+yVOYmlfw23GUlB0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-contextmenu.min.js", "FileLength": 4910, "LastWriteTime": "2025-06-28T04:51:41.1028855+00:00"}, "xxU6am8z3nm59H11T930Zj6j8vi+H39PSkF121F/Kxk=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\12tizwxlsz-u9z8issark.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-dashboard-layout.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-dashboard-layout.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "w1dz0kgdm2", "Integrity": "1Dw2C3AH7iMFfpyjMoMSG3izaxpPkV98+PoSJsmIkIc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-dashboard-layout.min.js", "FileLength": 10423, "LastWriteTime": "2025-06-28T04:51:41.110885+00:00"}, "aMUjw5uAcp8MrNj5KXrt7uI5c3PKSL+orAkQj/v4jJc=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\koe3iulnl6-yc8jpfd7rf.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-datepicker.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-datepicker.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2k4r0vpwdd", "Integrity": "LkRh1nvZKn779FaJWpRWXIg9tVswQUOlP47ZleIKjMs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-datepicker.min.js", "FileLength": 8853, "LastWriteTime": "2025-06-28T04:51:41.1188826+00:00"}, "qfX8uCcJjNIDgR+7Z/TJzQDzTHDFk3x6Q4dIxfs5zA4=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\cpm9m8wx2t-2o6i5e8wua.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-daterangepicker.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-daterangepicker.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ilkp5q6ft9", "Integrity": "et7nYwV7gOOzkmcFQV1qeTb9wqMjrVPgJUrXbhFwPQw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-daterangepicker.min.js", "FileLength": 4034, "LastWriteTime": "2025-06-28T04:51:41.1088853+00:00"}, "AgR+OdOHd2O3QP/Szvk13qNJwBKjo29bfwy+dqMYJtE=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\d4g29a2e03-z6tplterzc.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-diagramcomponent.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-diagramcomponent.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4pdpqlck5u", "Integrity": "mJ/K2SYXPUYt1FSYiDhG1jU/f51xOGfQ+B6LpXUtsfE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-diagramcomponent.min.js", "FileLength": 16479, "LastWriteTime": "2025-06-28T04:51:41.1198851+00:00"}, "WGNtphC9FUZDrns8QHp/Pb8Dmekj6yJ7SpXYAIPLbdk=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\xc1o58ypca-pcyq5jhqpn.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-dialog.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-dialog.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0mya0r2ez9", "Integrity": "SEZMahLFPSXemPk2uc56MCrMQisedLE1x2FUPB94EWs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-dialog.min.js", "FileLength": 5957, "LastWriteTime": "2025-06-28T04:51:41.1228841+00:00"}, "GyAx/eisb0k5R0qwwfLJrTDM3lNtacdqZfIyG8vdBHg=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\e2fdz9um82-pgf74sht8m.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-drop-down-button.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-drop-down-button.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ids8q06eai", "Integrity": "pnvUoWDDt0Z4/8ptJ6WpqS529TyaOUczQixu3ZPdP+U=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-drop-down-button.min.js", "FileLength": 2727, "LastWriteTime": "2025-06-28T04:51:41.1258848+00:00"}, "TtajcYiY8P8YlssnI3LFTpOsSdPVb1yP41em92X+8Yg=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\3s9nhql60o-nr5aox3wxc.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-dropdownlist.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-dropdownlist.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "zqa0qj8ajc", "Integrity": "lUPx1JV+XOX5+5tinPiYEEWI7CDy9gMnpDfsp9GClgs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-dropdownlist.min.js", "FileLength": 9781, "LastWriteTime": "2025-06-28T04:51:41.1308837+00:00"}, "9xBqMnyu9b9M3ZB8E+zKEf34ciqaOBX8AF/6xnlUQuM=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\ebbn4z5zip-bvcgyrt8rd.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-dropdowntree.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-dropdowntree.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "emrlcxwuil", "Integrity": "6lCaonxmq5exWfHPmO41pkhGt073K4BXp1rRSphlq3o=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-dropdowntree.min.js", "FileLength": 5963, "LastWriteTime": "2025-06-28T04:51:41.1418828+00:00"}, "6t0SobkkmgqdQiKsbiwrehQrm5195jFScoB6KFnNU0g=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\w2cakg9ukp-9vw6btwbk8.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-filemanager.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-filemanager.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3tksryv1mc", "Integrity": "V0vZlGPFnARJA8SqgcdLmNMx2m+jjVKiiDRJbZ+nCHw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-filemanager.min.js", "FileLength": 7036, "LastWriteTime": "2025-06-28T04:51:41.1550266+00:00"}, "4XKAbZO4VJVMqrVEBHfQHJ69meR6iGkpsCbWUt6xHuI=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\uehbyupll5-hc951xmi16.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-floating-action-button.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-floating-action-button.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "g8ndkrcmsj", "Integrity": "ICnAMbAHziHspFApEYXzS1gYN1UzH5AlH+0WNQIMZTw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-floating-action-button.min.js", "FileLength": 961, "LastWriteTime": "2025-06-28T04:51:41.1670284+00:00"}, "LWIFSLvsoGxPAwZacbBQHL6Cbo/RI/MKntETsCVASrQ=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\6ytg84iue8-f0bai7ekdd.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-gantt.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-gantt.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ejnwdas9v1", "Integrity": "KtQn0Co6e2MlNnPhtcQWuL+dkpYhTMNB7V4G/L9sE2o=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-gantt.min.js", "FileLength": 15059, "LastWriteTime": "2025-06-28T04:51:41.1268857+00:00"}, "uPNZqI6FB2fG7noOUduyTI8bG6FzguRMaUGtyrF68kg=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\fidxlt0g3v-mr284938c4.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-grid.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-grid.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kda62enhnt", "Integrity": "+JurwBcDTcLH2srNAn0cB4ydc+2jSqSRDJTChMNVT6U=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-grid.min.js", "FileLength": 63633, "LastWriteTime": "2025-06-28T04:51:41.2020239+00:00"}, "RpcJCm+MfWM11y38ZaxcU6THvC0tLXxyCY+crLM4jac=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\82nczmsris-lgwkt5cbc1.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-heatmap.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-heatmap.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0nvb6c5rfh", "Integrity": "7PpATUAg7+gesmtP7jm52hQt+55KaoX+da71Boyos8c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-heatmap.min.js", "FileLength": 5391, "LastWriteTime": "2025-06-28T04:51:41.3974652+00:00"}, "UoS6UjRJjIsYSBJ5aG4+OLV/Rz+4RHqLtpOhTRYcAAk=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\jjmy76blcj-dcctuye5cq.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-image-editor.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-image-editor.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "x77jawbpnd", "Integrity": "TWXHTo3iVOo2LjTcFmuThnB9DE8mpYitcv6VQn5l1C8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-image-editor.min.js", "FileLength": 154813, "LastWriteTime": "2025-06-28T04:51:41.5024861+00:00"}, "+u+dAqheiEV8tay7Bnsnw3DAKfO5Tr/S1x2rsaI1Dnk=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\vlup8812vg-2954plb0s1.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-inplaceeditor.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-inplaceeditor.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "6gg07h0t29", "Integrity": "Rt4LkyDsIYt5KhiMDGXbgyjnS1haCl9P5hv050/rxvk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-inplaceeditor.min.js", "FileLength": 2199, "LastWriteTime": "2025-06-28T04:51:41.5344869+00:00"}, "c+slbXDwuZuJhf5DoHxYrmBVGkghkBTsKEZ4+Qj9e+M=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\vjes1wfbht-ia4r3z0gfz.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-kanban.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-kanban.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xmf69eu42g", "Integrity": "qRUfom5gCaQbMbWZdoALLpy9+yy5ZZkOC2HwUD/W8zM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-kanban.min.js", "FileLength": 9539, "LastWriteTime": "2025-06-28T04:51:41.5384858+00:00"}, "Dw1FrCZIiWXdu+efX8jfEBA6OSIN+5TQ1tMDZBF1dys=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\cjlbdrwbmy-0xl8yilynt.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-lineargauge.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-lineargauge.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vjzkpw5klo", "Integrity": "BYZO+C95VzLxc+4eNQeon0dWXP28HF8KLsvjHgAqnjk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-lineargauge.min.js", "FileLength": 5172, "LastWriteTime": "2025-06-28T04:51:41.0060868+00:00"}, "MhjEl0yivagtkbJiJP0BgVnthjchcaRKQpuRdKs58hs=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\hpxslzcfcx-1ky2pdpltu.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-listbox.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-listbox.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "smdsotzljk", "Integrity": "BFB9Z6AIfKA15icIalPUlv95x/dP3fd/ivAZjkVlkUE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-listbox.min.js", "FileLength": 2115, "LastWriteTime": "2025-06-28T04:51:41.0390837+00:00"}, "rnifI1hozxp57LGYJKDn/ZFKKHjWF760Cco2bsae0XI=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\czt8ptpkp9-jdl6wrfi9e.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-listview.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-listview.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2dsj2829pl", "Integrity": "VtjxXAukNfYD/iogih6fKfbtEY2SgjJPoDERpqWKPYI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-listview.min.js", "FileLength": 5397, "LastWriteTime": "2025-06-28T04:51:41.0090875+00:00"}, "PKIY7XlVqtOuOR+eq7HzUohDVSoWR5rF1E8ASpx6yJo=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\7s51rvoei9-fwo6kylscv.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-maps.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-maps.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "wmr74s4qsr", "Integrity": "WETTUTq4bKaIglaY7gI6Hs9q/uveR0JjV1TRESOQlXY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-maps.min.js", "FileLength": 29078, "LastWriteTime": "2025-06-28T04:51:41.0494866+00:00"}, "6HPGFAyBHKYxtj+mXMJM3hliPvWQZqhGmKp1XtzRbN8=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\iukitgqwf9-qpsxgivx7e.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-maskedtextbox.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-maskedtextbox.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ejptcrv7h6", "Integrity": "dlF4LFFk9Xf/t8Cg5Ll+fZgCWIqqOunBKSqJffrGPPQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-maskedtextbox.min.js", "FileLength": 2775, "LastWriteTime": "2025-06-28T04:51:41.0664791+00:00"}, "Xnsrj87teDxSrMI0t6IJBM9wxmO1/wl01y9mQx7wN2c=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\be1fr8o4r3-4twzgaju3m.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-mention.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-mention.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qhqsss9dle", "Integrity": "4i72uH5fn/D5x5qK/oRyJN7q+1IA1Cv4+3NUg1Fzt0o=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-mention.min.js", "FileLength": 5986, "LastWriteTime": "2025-06-28T04:51:41.0998843+00:00"}, "NPniCEb2fe8MULe/mDmF6oDCccRqNivdLMWyhVNXIeE=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\gtnoncceba-8046lixhbj.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-menu.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-menu.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3p1xgx4joz", "Integrity": "17p/TdiaEF5D3o+ilQW5NKJQdhI6zKmVP6/2O6/2Z8E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-menu.min.js", "FileLength": 4105, "LastWriteTime": "2025-06-28T04:51:41.0853515+00:00"}, "FGc8CLKjvehCpvldV0vW0PgP0+ijZY607GDuPLudb/s=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\lkespo2mpc-44zb2tggal.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-multicolumncombobox.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-multicolumncombobox.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9u5fd8a5br", "Integrity": "UfjhKutm2g1UYb/PAAGaBnjN8MJlpSlBj0MrDJEE0Bw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-multicolumncombobox.min.js", "FileLength": 2340, "LastWriteTime": "2025-06-28T04:51:41.1448856+00:00"}, "LPyXRE5JrLzSW/4A+exHtmmKp/j0hSk+0FenPM6x4mg=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\xzqibnkirt-dmrg8y01sv.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-multiselect.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-multiselect.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sd7rm0izbe", "Integrity": "IBScf4PsEPNpN5A7c9DVMrE8HP/hRq0FkqpD+pin0bs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-multiselect.min.js", "FileLength": 8693, "LastWriteTime": "2025-06-28T04:51:41.1540265+00:00"}, "ri2At4HKO7VQ7D1Q681PDCjbBqV8413wrYTPwXARTYM=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\itlbf61hm6-foj9kxj9yt.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-numerictextbox.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-numerictextbox.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nt8w2oyub1", "Integrity": "VclbsD6fN8q3rikUx7v8Q0gcNOhH0e8LG7iZiORYnSo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-numerictextbox.min.js", "FileLength": 3256, "LastWriteTime": "2025-06-28T04:51:41.1570272+00:00"}, "5fQKHoflODBSTWw0SsKr6LlRM/jwxViQgVJq3PpfWSg=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\s64mkyknem-n4rs9723y7.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-otp-input.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-otp-input.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kcu5moe55p", "Integrity": "HIGVUKH/7L+6+2sbSLDbN4iQ6aYSTP4PVenPPHzA3YY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-otp-input.min.js", "FileLength": 1156, "LastWriteTime": "2025-06-28T04:51:41.104885+00:00"}, "qMRYlsQg8NXbmNCBsbzQq3C+MzV4fiQ8i+6hu++p41A=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\uofcb8dzuy-qm5c43mq79.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-pager.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-pager.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kx5lf6alou", "Integrity": "SDjBBpcTMQMFRf9EOM3Z7yHlSKiPfUnpncAkj4tD6q0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-pager.min.js", "FileLength": 2604, "LastWriteTime": "2025-06-28T04:51:41.1078839+00:00"}, "X+ET1tJ4sAkJiO0EQMzMoUI0GWpeJ/nxaVvV5jyh8lU=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\dkkcf86snh-arz65ynj05.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-pivotview.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-pivotview.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "c888qzpplv", "Integrity": "rHGC8e8cX8tCPqk9YoErgHyJf1H1w3IcX2gimnwpbXM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-pivotview.min.js", "FileLength": 20145, "LastWriteTime": "2025-06-28T04:51:41.1228841+00:00"}, "+w8bfmL+nf/NuEpJHy5WsOEEp+GpRxbVJknHa6MXL2Y=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\apf3r30luq-f7i5btrdbc.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-progressbar.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-progressbar.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "6w71how2y2", "Integrity": "Nm8s8aVVR0Y7aDE/7jI/YYNF8jDqn9KOEE+fihGoya4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-progressbar.min.js", "FileLength": 3845, "LastWriteTime": "2025-06-28T04:51:41.127884+00:00"}, "OHmnyFzMr3NGqu3S4OXY4fcEzBwRaX+g4tfCMpnPZf8=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\9h9zfn7yek-ef44tjx8e3.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-querybuilder.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-querybuilder.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "pc12kjzscy", "Integrity": "p5zRzMk/DHPEs+m+GtDlimKrbNnB9FBCNB50brCWyDw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-querybuilder.min.js", "FileLength": 2043, "LastWriteTime": "2025-06-28T04:51:41.1308837+00:00"}, "Poodhr9XcFj7lAnZmYCOre0i6pU4E/t6YiybUbFhaaI=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\jcgtm963n0-xnv1h531ft.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-range-navigator.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-range-navigator.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jh6gqqn4kz", "Integrity": "KQlCQjyZb86GTuZVMNbRVoGPR6hhXo+DhMHFcPtXneE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-range-navigator.min.js", "FileLength": 4400, "LastWriteTime": "2025-06-28T04:51:41.1418828+00:00"}, "huxcRYvH829ie63l0xDTt7PI8VWuOOiR0if1xm0xhmI=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\6xby58i1ne-v3757l2xq9.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-rating.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-rating.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "465m6wj0sp", "Integrity": "rDlMlTSGeWxKJFnzdv0LftVyb4W4FJ3igQwJ4LEwc48=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-rating.min.js", "FileLength": 2494, "LastWriteTime": "2025-06-28T04:51:41.1480301+00:00"}, "souuQC32KgksJ/tbnU//9M5aMhGGK0JXSLqc/XYCiMw=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\5qjtvvhsgz-6wcp4asz4u.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-ribbon.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-ribbon.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "zefc5pu079", "Integrity": "9R0+ubmOwAvNTwebGHqKQ9q5Mg+MJiDm0ZFenKZOdus=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-ribbon.min.js", "FileLength": 15744, "LastWriteTime": "2025-06-28T04:51:41.1800261+00:00"}, "PuinYp68gt4Y9DXqhrY0hGDrUTAiKhlICRTSPD/EyLM=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\16wg0iqttf-9c91qen16e.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-richtexteditor.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-richtexteditor.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "o3p9f3o435", "Integrity": "VMIwOTbsa300Bssr89VQXQAaXQ0W22JZLr5OclOhpI8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-richtexteditor.min.js", "FileLength": 197274, "LastWriteTime": "2025-06-28T04:51:41.7160068+00:00"}, "/hd1cfg7FKoVwzySAYeqWJ0guYw+6fFwq8+ai3j47TM=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\qxbdc57p9r-6n7x6zz8tv.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-sankey.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-sankey.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7jeon2hvj7", "Integrity": "k+78hsnOdCZYGYTzc2NGQhSpeNePy2UqrWjJjjwwzcs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-sankey.min.js", "FileLength": 3329, "LastWriteTime": "2025-06-28T04:51:41.7430096+00:00"}, "PKQm4EOBapAPm4dxlhE1OnPeNj1gDhBfYHDd0SNUR9k=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\3a4nupymxx-vv7s3npc8e.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-schedule.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-schedule.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kjfb8bfjb6", "Integrity": "cOedq4wz2HVeZESrENLCh7weMpBTX61wg3bDalL44j4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-schedule.min.js", "FileLength": 51249, "LastWriteTime": "2025-06-28T04:51:42.260584+00:00"}, "cN277LZiwnB5TmQgH0O2NVlIrj7naqmI91D95JM7TXA=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\42ck71d2wv-xi21sm924f.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-sidebar.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-sidebar.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2revezkjll", "Integrity": "bpbW1EmLTU1a6Dd4swS6jfEm9wyTkhE5lTfaKo5JOgY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-sidebar.min.js", "FileLength": 2960, "LastWriteTime": "2025-06-28T04:51:42.4203758+00:00"}, "G64Yj8wEiEYtm5pq8FQcfnbFv9u7ksjNo3a8j4v+8N4=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\hruzog0nao-rhae2i594o.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-signature.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-signature.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3c045vl9z7", "Integrity": "TZR3ioiMzg7Ky2Xq7TTqFuZdaKuHdqX0BpR094a24fM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-signature.min.js", "FileLength": 5960, "LastWriteTime": "2025-06-28T04:51:41.1800261+00:00"}, "rtanUDQ3MXzM72b/pSJCTfOW4Krc2Eh5P/Vx/fRu0yI=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\8z7q0tcoh7-kfovwrgj95.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-slider.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-slider.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sx5f5xw1la", "Integrity": "MKuIXjuRW2a0DI1Crgwv/cdhgiLzO3i+nZS9i22GqBo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-slider.min.js", "FileLength": 6451, "LastWriteTime": "2025-06-28T04:51:41.3534645+00:00"}, "AvJLkrtIC6c9QFqRkN05IXdsEm4eVvO5wDNeLUFKO2o=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\r4s30r6yiw-cxkgj8b5g3.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-smith-chart.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-smith-chart.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "tv70cufhww", "Integrity": "6RuXeBrM/YVnheaw5xhiZkVLFDypDThsgEnjqWBFg4E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-smith-chart.min.js", "FileLength": 4036, "LastWriteTime": "2025-06-28T04:51:41.3974652+00:00"}, "FsMKwrZ/U8uAF8XQe1Qppr2lpcM5d8hnGyaleZOmqQo=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\6jncl9y7mi-ns71o37rr9.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-sparkline.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-sparkline.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "43ark85yn4", "Integrity": "0O8/MXLpcWFeYdRJH/Ds6BxGJZ5RbtqMregVEx2vmCQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-sparkline.min.js", "FileLength": 2404, "LastWriteTime": "2025-06-28T04:51:41.4004655+00:00"}, "SgJpKPUv9ZmBpZrDC+UvygBEkkhSqWfLU6PXMdorZTQ=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\lsawkhetsb-z5kdtrfyru.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-speechtotext.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-speechtotext.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rw823r4dp7", "Integrity": "ZbeHDQCLkYHI/eSoE2kH88YvuSbXaGufxCtw3yu0RBE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-speechtotext.min.js", "FileLength": 1112, "LastWriteTime": "2025-06-28T04:51:41.4024651+00:00"}, "vD3/AmqAYRoDkW/vgGY6b0bU29F06kTvqdhk1hSc4Kc=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\3ex2m3if29-sd12zoa8l1.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-speeddial.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-speeddial.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "wh5n6kw2ef", "Integrity": "lJHHobopq0tDEFe1olwCrhcyxCQb8VO67beMpawrWPU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-speeddial.min.js", "FileLength": 2741, "LastWriteTime": "2025-06-28T04:51:41.4054654+00:00"}, "WPiHjzUlBq3x8FSH2M43HCedhuuuM54U5Pb+/ZvkXrg=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\0rac66dpmt-ptfovnb1pb.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-spinner.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-spinner.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "6vqgkvrk4l", "Integrity": "/vOd61AVE/7cF7w3t+XsF7jjwzR/yZ3E3sTT7cagqU4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-spinner.min.js", "FileLength": 670, "LastWriteTime": "2025-06-28T04:51:41.0010865+00:00"}, "VdSshficBVsDctGUlb0cuTzgRLW6lUtXIq8RuBV1OR0=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\aadd9iinhj-m8t8qzdyur.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-splitter.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-splitter.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "30fhlvt2bu", "Integrity": "M8fUEaHlQr9l5BUdUPOhlKfpX/NsVF2X1tlnj9QEmbk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-splitter.min.js", "FileLength": 9478, "LastWriteTime": "2025-06-28T04:51:41.0664791+00:00"}, "xdAtFfre4YLrrErubwRPtlZ5N8jorlsRMazdj/dkpCI=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\nxqjd904l0-2kzoovsebb.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-stepper.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-stepper.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "n88wr992a3", "Integrity": "ST+7n1/3Bsj6jF0ELJjYrYRx7lCvQL+lnuw7pFBsMII=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-stepper.min.js", "FileLength": 3579, "LastWriteTime": "2025-06-28T04:51:41.0030885+00:00"}, "kieRqLlPXo70ZAq2+AGV5NRjktASOwv0ABCASfcFPss=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\5esrip7fz2-k51som5grn.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-stock-chart.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-stock-chart.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yie5d5nvhl", "Integrity": "ymnfyHJyFF99YsQ8pI45mt4iNjrBkHxdC6YDLCtA/YE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-stock-chart.min.js", "FileLength": 3659, "LastWriteTime": "2025-06-28T04:51:41.0050871+00:00"}, "4Z3lvJJYg0+ymkbCB37pHJSUE7LZnjTQ0BtHkC6srIk=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\zr19gib3w2-usj0zbv0qa.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-svg-export.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-svg-export.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4nu90xco29", "Integrity": "oBjZbooHflDdgTVuaASinLxm5SRD8tq1hnm7weLXLc4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-svg-export.min.js", "FileLength": 3884, "LastWriteTime": "2025-06-28T04:51:41.0070891+00:00"}, "Mv2H7mJCyjVfcUKuqn88JDO/n74eVNyxycTBioD0+jM=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\b3svqv6skf-6szfu4qei4.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-tab.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-tab.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cx6skesyf9", "Integrity": "8CF5yIoH5FrYxPnGJc6aLuSmbsXaqSrZz3hCZdYp2fo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-tab.min.js", "FileLength": 7867, "LastWriteTime": "2025-06-28T04:51:41.0110872+00:00"}, "Id0aMIQEAckrD65sSbZgiqwrjpKwWw9I2UHKKuM4eQU=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\t9ldm31sr4-ik2utc94vi.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-textarea.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-textarea.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vvvmzq9xlx", "Integrity": "68VcsdkpzuYxX9Hb1nUkF60VQ4eP6kjYtIZhYf6M+Cw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-textarea.min.js", "FileLength": 810, "LastWriteTime": "2025-06-28T04:51:41.0140911+00:00"}, "nEgXvQ4Mw2IEyoo4yaq8WCX5Cpa4QHy3gA5ghreF+uA=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\5sm0ch5rs2-4wgpeatwr6.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-textbox.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-textbox.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "me0fcah4i1", "Integrity": "MkwowaLTF4bIT8mPcWV5loSVnPJ8dKsxlp8qQQ/ddeE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-textbox.min.js", "FileLength": 1298, "LastWriteTime": "2025-06-28T04:51:41.0160913+00:00"}, "P8z10h0OjlsgILNGlPE7bqP5gPk+RjtlpRB3JGJvuvc=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\8u9jrxeb4z-zetsjdgn5k.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-timepicker.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-timepicker.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9od1rj76il", "Integrity": "h+17dUsklyygG9h0Lin80VVxgiYT5ZjXHOSeJJArQ7k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-timepicker.min.js", "FileLength": 7657, "LastWriteTime": "2025-06-28T04:51:41.0202084+00:00"}, "IbybrlVMS8bEnDQi2BuW8RBq2o+v4J7CQioazMKIfuw=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\qr1eqiq81d-78yoonbl0i.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-toast.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-toast.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "juyaxbjt57", "Integrity": "FfTYPCgzMuqSLd9Z3eruKjzBIKys9xR4jM4vXCCIY0g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-toast.min.js", "FileLength": 2428, "LastWriteTime": "2025-06-28T04:51:41.0232916+00:00"}, "mlCB7hr15VHoAD6DUhGUJBxrynAwTtfR7yLS+nUTp7c=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\qeg02v8nug-1mh64yn0m1.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-toolbar.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-toolbar.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "upf8es1t53", "Integrity": "bvAbefeUT223svzdFsoGZmBHizF7EjHU7yB9nCY2UVw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-toolbar.min.js", "FileLength": 9553, "LastWriteTime": "2025-06-28T04:51:41.0400829+00:00"}, "tTwX58GdyzRbJ7wlTruCVIx45r53FRokcWlQ/KOz+Z4=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\6iv2gqi52w-0h6wo8b5zj.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-tooltip.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-tooltip.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1uodgeftrv", "Integrity": "BwmRGyW/XSespY4zzben86kNaeGHDtsIQ62oPhFlm88=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-tooltip.min.js", "FileLength": 7490, "LastWriteTime": "2025-06-28T04:51:41.0853515+00:00"}, "dOVM6T6eU6ERQRgiBR99UgNhYpmW6wi/y+tK/1eeB10=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\d8h16lm625-oxabjc9tlq.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-treegrid.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-treegrid.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "r9wg2wsizi", "Integrity": "Xwk1Ie7W0+wAmiFn7fWAwiD8IWZCuIcn3RLurvCoalg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-treegrid.min.js", "FileLength": 7660, "LastWriteTime": "2025-06-28T04:51:41.1028855+00:00"}, "ILTv2Ou7bfcnS4mwaDnJKV1Mv2AHi6KqV/SgIa4hx6Q=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\tuovec8evf-47x7e23k45.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-treemap.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-treemap.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8tdtbxquf1", "Integrity": "LBoUkXAtdrkh2vYZ6dpvDhv/C1MRykmmBaSMXb1bEpU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-treemap.min.js", "FileLength": 2917, "LastWriteTime": "2025-06-28T04:51:41.1068861+00:00"}, "AMslRGLCJmX6ANXP/7+qZ+sELqPH3+F6k64c8GjyvmU=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\1ad0wz8wh3-lcx92424z7.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-treeview.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-treeview.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ai9f48i0t6", "Integrity": "Hl7ldiPVAIvnoV1oTFk6TfXfF2jbUMrhHbBI5khFONY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-treeview.min.js", "FileLength": 12153, "LastWriteTime": "2025-06-28T04:51:41.1148843+00:00"}, "CuA1Fjl9TjXBw40cHJxq7P59Ukjem9MNUPGumOqdO8o=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\j3374ns99t-qphzxvv8ff.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sf-uploader.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-uploader.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3h6dv14h2g", "Integrity": "+fog0zlmqSKddbcTd17x2kdGwIL9vB0GPEm1FYVKG4k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sf-uploader.min.js", "FileLength": 19249, "LastWriteTime": "2025-06-28T04:51:41.1238842+00:00"}, "Rlxpzuf/7kg3izvQ1e8pnQf7l5Px1LXUkn1BRUPGkCc=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\ybfh3feh32-gz6e3atemk.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/sortable.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sortable.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "imgsbjhlkh", "Integrity": "1lV0KfopPnWwTUJ0eepDTzZg2d0csvU+xxPmXAlegeE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\sortable.min.js", "FileLength": 3142, "LastWriteTime": "2025-06-28T04:51:41.1418828+00:00"}, "iI1ZD7SfYyuYGLwvjmqWYSNrENRr1Z/9AZOeFx4dyGM=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\0b5pm8t2xd-ap0tuxedpx.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/spinner.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\spinner.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rbrjqej3qc", "Integrity": "6ayPRUlyr2jBIpczN3xpfl2b5cXKoXJ+JKLcEtBHlF4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\spinner.min.js", "FileLength": 3398, "LastWriteTime": "2025-06-28T04:51:41.1490292+00:00"}, "mwlTia4rb2RBVVsrdzfLbDRy3+B6+0YGOcYjmt26HzE=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\ancwpmpcu5-4dvlhbz2ty.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/splitbuttonsbase.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\splitbuttonsbase.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rx6z1naeog", "Integrity": "34qdEC017VJ+cuWFELIFyA5IyRLDmQFb2DoSED+soq4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\splitbuttonsbase.min.js", "FileLength": 1447, "LastWriteTime": "2025-06-28T04:51:41.1268857+00:00"}, "M0O5C81BeGOlC1Vd7HOReRBXwUuuQCIw05FkRbB5w3k=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\spxlaub6dv-372y316ixj.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/svgbase.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\svgbase.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "v1ed2isd95", "Integrity": "na03nZTJkoL8dxcE4fga3CLmJUfzIoZSS7jeF7VnmGM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\svgbase.min.js", "FileLength": 12720, "LastWriteTime": "2025-06-28T04:51:41.1418828+00:00"}, "5dKUOdBcfTotvbHYIoIgM831ph/5gq0Sb8s7/IgiDAY=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\tt93irvsj4-q1js77zo12.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/syncfusion-blazor-base.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\syncfusion-blazor-base.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mpr3oq23wl", "Integrity": "Ck87z33Puj8o+MA4T/KUSSO01sHpf/AQSNDwoJ1AGGE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\syncfusion-blazor-base.min.js", "FileLength": 100382, "LastWriteTime": "2025-06-28T04:51:41.2200242+00:00"}, "o0oXem58lutb9NIcCDGg7R6D74pVbG9OdTXLBgOfvWU=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\bqvff7ljya-vcp2sac2s7.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "scripts/syncfusion-blazor.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\syncfusion-blazor.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "u9pywsc8ms", "Integrity": "coQTsRcNC3ID6ac3LEhW/qHUKP53hTNaEvwJ+VAki1E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\scripts\\syncfusion-blazor.min.js", "FileLength": 986407, "LastWriteTime": "2025-06-28T04:51:42.2945798+00:00"}, "QrfoU9BMHT18xrqoSCIOzOwYCoeD3vMGjoOMD+pJaUI=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\yvsznq4373-i2g872140o.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/bootstrap.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\styles\\bootstrap.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cr8br0eb2j", "Integrity": "6GN2r+r1CoSpfAzxQuuTZrKWmGTSotpCr+RDdnVTB1c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\styles\\bootstrap.css", "FileLength": 476009, "LastWriteTime": "2025-06-28T04:51:42.6416608+00:00"}, "s/UHxb+6kyO6vVgZ+bMiI3ZZhDaY2ZFKl2mAjrfYcKI=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\xo1my5bok1-c40fyl3ara.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/bootstrap-dark.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\styles\\bootstrap-dark.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "92frdq3qn9", "Integrity": "//U/mCqxk1jFjtIW7Gf+AtpzcxhzUpqverMS3Myr/P8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\styles\\bootstrap-dark.css", "FileLength": 475446, "LastWriteTime": "2025-06-28T04:51:43.089969+00:00"}, "F9XppsJFDnZ/PgqSqLXuQhqT5MPRDVRQfu1a5U3zO+Q=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\86a35pd8i9-iz9gyka5i9.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/bootstrap-dark-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\styles\\bootstrap-dark-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rjm2w9wowk", "Integrity": "WT3SkLlQxIvr+I4F9iakIgKx3+GPihj+HVKipaSlVXc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\styles\\bootstrap-dark-lite.css", "FileLength": 475449, "LastWriteTime": "2025-06-28T04:51:43.3450264+00:00"}, "attWzhQEA7UVlXQubW7rmC334IkoNy3WDWxiqiNYhmA=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\8503zsol33-5q9lwcsdf0.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/bootstrap-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\styles\\bootstrap-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "23f1jiu1cz", "Integrity": "np2B+Xp/zpFTYiHoRhLfDzXWSjulqv4He0p6gIP1Kpw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\styles\\bootstrap-lite.css", "FileLength": 476017, "LastWriteTime": "2025-06-28T04:51:43.7182994+00:00"}, "tP029Mz/uqENK41ROUuw3WceD/iaWBcGVYJmULpc+MY=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\34yiq18zxz-gz1e1z4pqv.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/bootstrap4.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\styles\\bootstrap4.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xorkwtx4al", "Integrity": "MY3fNazEEZMNsVE84IXPNR98FHHQlh48ME/jVjwlT2U=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\styles\\bootstrap4.css", "FileLength": 466954, "LastWriteTime": "2025-06-28T04:51:44.1935146+00:00"}, "sdUhnvJtX70WVhLK1gacoQsSdumyO4Orh2NWc/SZycU=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\rugulc3dbo-jjs54pfh5m.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/bootstrap4-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\styles\\bootstrap4-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "m7f2di7yvr", "Integrity": "gl43XETDODIpxO+UoPMfSEmkT9uL7LBvfT5HCasnhg0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\styles\\bootstrap4-lite.css", "FileLength": 466959, "LastWriteTime": "2025-06-28T04:51:44.4396743+00:00"}, "WAwNpoW5v8DLJJh1DJcntKJTmCHP2hoTRpy7iOFguKw=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\rt6g31qov9-qhde89oftt.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/bootstrap5.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\styles\\bootstrap5.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ee0jcom333", "Integrity": "xpQVOckGkwkB9Ob2OrERYeEtxW2jVC827o4VNhsi1Nk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\styles\\bootstrap5.css", "FileLength": 474304, "LastWriteTime": "2025-06-28T04:51:41.7270046+00:00"}, "vcMNg8nNWyJdJIPibKIN5tfY7Kurmv6ff9ZYRbnPxSM=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\16lpm8pp05-v83pxlq744.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/bootstrap5-dark.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\styles\\bootstrap5-dark.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "gd7nxdizls", "Integrity": "FHpvvIOqTIAnmhKhokfNTD1cHJHxPaFIWlRI/I2/PuQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\styles\\bootstrap5-dark.css", "FileLength": 478165, "LastWriteTime": "2025-06-28T04:51:41.7370046+00:00"}, "i+1bXyPuPFkDeMlEH6wkckQgBtxyuuiawDAXX9rCmSc=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\7hpqi5txla-sdz3396v9j.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/bootstrap5-dark-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\styles\\bootstrap5-dark-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jsuh7lm44b", "Integrity": "3Q7s2NJa8QXJ70qJe/+J9BUXT6ckgggvS9UpdrYiv2o=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\styles\\bootstrap5-dark-lite.css", "FileLength": 478171, "LastWriteTime": "2025-06-28T04:51:41.8015828+00:00"}, "glvCF62IcEyuARmNAQl0QmzfYft3G7m/tDB69fYNPXw=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\apybc81neu-7blbp1mce4.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/bootstrap5-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\styles\\bootstrap5-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fdt1ip4895", "Integrity": "8+AzyicdoNZiJUdt6YKbjnbYC72UsDzSJq7Gz59bjR8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\styles\\bootstrap5-lite.css", "FileLength": 474310, "LastWriteTime": "2025-06-28T04:51:42.6896616+00:00"}, "y55SjTyFs61jGR3EMxev6Y7DVPbjoOErLyUp9VOFzQI=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\uoq7cj833o-wv5ulmhyte.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/bootstrap5.3.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\styles\\bootstrap5.3.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8yaz4475yw", "Integrity": "MOb8OA91I1d6dIq71XKnmRRijSoUr0IBZlHRwRa4nBQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\styles\\bootstrap5.3.css", "FileLength": 479990, "LastWriteTime": "2025-06-28T04:51:43.2054811+00:00"}, "B9jtHZ4aE9sJmR7+x3dGbDnG3543mrcFVDvf/UCu3I0=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\0ljcaj2py1-kebdmujib9.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/bootstrap5.3-dark.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\styles\\bootstrap5.3-dark.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9i0telrpk5", "Integrity": "LyZiKqE1ElPWgfLmiioT/pUNTsQZGUXIhR8vY6PCw1M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\styles\\bootstrap5.3-dark.css", "FileLength": 479086, "LastWriteTime": "2025-06-28T04:51:43.7471461+00:00"}, "mpTJik3Aofq3JW0R0czQ1YXTNDyuVh//58IUyBjw1DA=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\09tjyrmpyg-xk8az7f4fs.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/bootstrap5.3-dark-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\styles\\bootstrap5.3-dark-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2sz6demyzp", "Integrity": "XgCywPS4/zXpBCVHdhjpvTX4C7nG0m5V7f4exErbbSM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\styles\\bootstrap5.3-dark-lite.css", "FileLength": 479088, "LastWriteTime": "2025-06-28T04:51:41.8254987+00:00"}, "soSnvBMHQo8bzQlcHZZyyxFgjq5La1AJs+ndt09GdEc=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\r3cgco6llb-ef581g9zll.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/bootstrap5.3-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\styles\\bootstrap5.3-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ucja2f4j5p", "Integrity": "fl5Oaq4iiyU9arLEkx2bp/JMApwb/vBl1Mk4LhKV+Hw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\styles\\bootstrap5.3-lite.css", "FileLength": 479995, "LastWriteTime": "2025-06-28T04:51:42.6796627+00:00"}, "lT4RZAzoUuQadO/gFGYh0ih4IgVLWFJMhX692iNU2zQ=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\3uil544spb-6zshlbbixp.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/customized/material.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\styles\\customized\\material.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "6ru5la4yh6", "Integrity": "8ofipkhbDN0jzlwJYvtNIl4wzLm7N64bgA2iGunMk20=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\styles\\customized\\material.css", "FileLength": 503997, "LastWriteTime": "2025-06-28T04:51:43.2145854+00:00"}, "B3FT0IoFflGsIlbw5rqJkn0DDDoHxQTdI7BdaCIGIoA=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\0z85kndwvu-umulwh652s.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/customized/material-dark.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\styles\\customized\\material-dark.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "a9sgesectl", "Integrity": "DpFrjbk8f69zGnHSjiDSnxmTXiAT2KUgco1v9PfRNYw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\styles\\customized\\material-dark.css", "FileLength": 510474, "LastWriteTime": "2025-06-28T04:51:43.78952+00:00"}, "Wiz02uCyQIaXrU5eOIn+yqXLjS80A1m/J+oOYlOK7Ek=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\bxxz62r4fh-zm9rzsb9zl.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/customized/tailwind.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\styles\\customized\\tailwind.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kiiaayfoae", "Integrity": "PH4beuGJV6dB5n8kaJphTGZXYRwmZGGsnQFuhztcICM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\styles\\customized\\tailwind.css", "FileLength": 438439, "LastWriteTime": "2025-06-28T04:51:44.1955142+00:00"}, "0DrpQpZhD/GZJYPJRj8u6mMRE+UcWPYQuSvXDxa0Mnc=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\97jndmeew1-ozbnyg3t5p.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/customized/tailwind-dark.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\styles\\customized\\tailwind-dark.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "c4cw2z891r", "Integrity": "pIxr3K2iM3ml7uGnZuguyGDMDvQnjfcZmZmBRft2/vs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\styles\\customized\\tailwind-dark.css", "FileLength": 441420, "LastWriteTime": "2025-06-28T04:51:44.4296786+00:00"}, "cm0ovb7z7eawVmWJgJawdkxfNH/HU276jRLNHJwqAZI=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\tkqxgce19s-5kxn8q7zaa.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/fabric.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\styles\\fabric.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "gmq3a4al51", "Integrity": "iGN4ha+nSU4x586zydJZn7zfue3q0k6qeQFQjllobOM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\styles\\fabric.css", "FileLength": 455482, "LastWriteTime": "2025-06-28T04:51:44.6332453+00:00"}, "sCCJJqmODAWC5Kuo+kCabMRmT327+8nude/XRBypgVE=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\giwg4o6lvn-yumv9blev4.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/fabric-dark.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\styles\\fabric-dark.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "f2eipxrasw", "Integrity": "tyTeQEjP5PY7YO4JSe+0pXZ8b3VkZXjDBpn9xSPOicY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\styles\\fabric-dark.css", "FileLength": 465452, "LastWriteTime": "2025-06-28T04:51:44.9701165+00:00"}, "0BPuA5Nwv4s5VRz1hzc9PcfA5r7fTV/8F+9OrsNLD6I=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\9exgy2h7v5-la93zhwvva.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/fabric-dark-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\styles\\fabric-dark-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "55ih2czzeb", "Integrity": "56cENWYkP6TEUPH5GEkeBaZpDzk7SqVvm15XXFtsqJk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\styles\\fabric-dark-lite.css", "FileLength": 465454, "LastWriteTime": "2025-06-28T04:51:45.1787686+00:00"}, "03xppT+kB5IWFow0uwquTJNoBamv+rSVES5AefQH6ks=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\0roaiwm1eq-xxwqt750y0.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/fabric-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\styles\\fabric-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "oggjq9g3xm", "Integrity": "6EfjlTmMrzXQuENHOqzACHzaqbGqUkA3VyJcGFDWWcE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\styles\\fabric-lite.css", "FileLength": 455490, "LastWriteTime": "2025-06-28T04:51:45.3518116+00:00"}, "QmKygnHHeRTyNAqNCD+i8UEitykdzA/om7Q3E6d1EDE=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\cpd57ko01h-8skt3q6tyb.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/fluent.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\styles\\fluent.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "96do7xkfdx", "Integrity": "TxwIGJrTGFLA7PUL478yeTMpNawTubHcDffESQFLIyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\styles\\fluent.css", "FileLength": 454429, "LastWriteTime": "2025-06-28T04:51:45.538455+00:00"}, "IRabvU9Itxn+ZmFJlKofOXAXD34Nh2pWQUFSwYivaUc=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\18b0yyeh6j-q6t3tx666r.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/fluent-dark.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\styles\\fluent-dark.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vc06i1ritc", "Integrity": "sZLvzkLBY9TewDrgdBIhhQzdGfYFKIqm1RRJNa2uP4E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\styles\\fluent-dark.css", "FileLength": 454755, "LastWriteTime": "2025-06-28T04:51:45.7241798+00:00"}, "TbCXIiS4nWEtql3YQtOf8pQNTy+R9/KG4j5O/+yzj+A=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\r5l6zmsmwz-td9ae9u3xh.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/fluent-dark-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\styles\\fluent-dark-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nv1xgrnqc3", "Integrity": "PSza2l6jrTXuUDkef1hQtPWMjT32RgdDW2DcdasAukc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\styles\\fluent-dark-lite.css", "FileLength": 454757, "LastWriteTime": "2025-06-28T04:51:45.9406772+00:00"}, "qE1Ott0YToCkmw5INy8kk3fT2lFSqD38c+0NVzwhWxk=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\w0f18brd7i-7bo1xv0y1t.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/fluent-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\styles\\fluent-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nh58gr8sbk", "Integrity": "y6nB9QBppY8Jtppr+pq5YkQ+2qSj38eaiNuRGv4IDGw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\styles\\fluent-lite.css", "FileLength": 454433, "LastWriteTime": "2025-06-28T04:51:46.1084483+00:00"}, "IcVOy3rCc/HgD5+qRtwcbGZyPwRjM569hxtbwlo/Zew=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\tzmdu8q96u-jqriujue3n.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/fluent2.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\styles\\fluent2.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "6b7x7ejfnc", "Integrity": "vg21+EIzglj5pNYs6R7f8WbQEhOCHKPuI7pJCEzKY7E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\styles\\fluent2.css", "FileLength": 490074, "LastWriteTime": "2025-06-28T04:51:46.620568+00:00"}, "KxU2aOtHM8jXtWlFeBKlR/Vq/QCkrRngHyCi1gglaBA=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\azz7n0gu3n-c6vvogfiwj.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/fluent2-dark.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\styles\\fluent2-dark.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vu80d73sc8", "Integrity": "1wUgA4s/lu98zn8mQ2l3ZDC7JjCuc+jdjVW1zNAobfc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\styles\\fluent2-dark.css", "FileLength": 487060, "LastWriteTime": "2025-06-28T04:51:46.8112589+00:00"}, "OfrmazM11JZb0H2Zl34mvmQlG9J8iRE/ToAAg170DE8=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\bu4pbrast1-m0rle8326f.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/fluent2-dark-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\styles\\fluent2-dark-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "se1y25y25s", "Integrity": "pFHCZjG5VL8uE5mAYolz9Axu++DYadIDqG/mDt/DdZY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\styles\\fluent2-dark-lite.css", "FileLength": 487063, "LastWriteTime": "2025-06-28T04:51:42.1754086+00:00"}, "AMbjrNy5tUSmCp0TJ5akecwT2Zoa2mqgNTlAYibbUeI=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\80ppwlqd2m-etsp7g833c.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/fluent2-highcontrast.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\styles\\fluent2-highcontrast.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vqagb3qqux", "Integrity": "Z5FhRj/511I59A0X3D3Io0fMX/08tCaPEDlPUZJ4F3A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\styles\\fluent2-highcontrast.css", "FileLength": 488579, "LastWriteTime": "2025-06-28T04:51:42.7357707+00:00"}, "o+LS2Kf3Cbn25okCZ5bB2tmeJWV5XiSfYLgQiGtXvIU=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\dazefiugh9-mrbdda3cj6.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/fluent2-highcontrast-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\styles\\fluent2-highcontrast-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "q3vpmbj21m", "Integrity": "tvtO3Zjp0JVSEgp3W7NYWKLfQ+/2hsB/2qYuIjZx+ig=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\styles\\fluent2-highcontrast-lite.css", "FileLength": 488580, "LastWriteTime": "2025-06-28T04:51:43.236589+00:00"}, "4b5u4dBTQEqIQ/3V4irUY2S+EFcKTyPBXbER19ZCIXA=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\2lur8dmyl3-c4l8bzzz3d.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/fluent2-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\styles\\fluent2-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "grqlezqx8x", "Integrity": "RGsrSCPvviyfUufIXf20pn6r1vlAjrKCWMwLsaabAEc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\styles\\fluent2-lite.css", "FileLength": 490080, "LastWriteTime": "2025-06-28T04:51:43.8268865+00:00"}, "HS43S11yTBtKWvFMlJYzX5Xp8FAf8hIPpNPif3xoFzA=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\wb6pcilptw-qfv16a8w3p.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/highcontrast.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\styles\\highcontrast.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "wo2nm1ou28", "Integrity": "oBhqSLOycK6e50r2qOmcL9LWr9m9JLMXgx0LxRWD+qA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\styles\\highcontrast.css", "FileLength": 453411, "LastWriteTime": "2025-06-28T04:51:44.2248646+00:00"}, "rslMnwkTl5K0UPAeAxoaDRPkzyM+Kt5b6AGggMucROM=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\jnn0t5sikh-i1y08pb6r2.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/highcontrast-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\styles\\highcontrast-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bgb1d6cqxa", "Integrity": "OzXOe6TWZR9EfNC7M3fNCrfnh/DDz5LiHPTwIJvzzAQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\styles\\highcontrast-lite.css", "FileLength": 453412, "LastWriteTime": "2025-06-28T04:51:44.4656778+00:00"}, "1NSfWbbqiMKsYm9R5fU2f2CtPUH3HmyarFNyeaBut4c=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\tz16aznt9x-ne2q2zvmee.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/material.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\styles\\material.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ihh7b6isaw", "Integrity": "usi4iVvo+UDOxLeNtlB/8xfUEfmQALwTLNxp/RAndr0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\styles\\material.css", "FileLength": 504051, "LastWriteTime": "2025-06-28T04:51:41.5744879+00:00"}, "BOsM5F4aUHaja5voz+jjp2ragbDcD+wswjyzngeKpg0=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\fr3spr8ava-qgqgkp4l86.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/material-dark.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\styles\\material-dark.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "os3d34y14m", "Integrity": "iNj0e+9oqk2otyvmmxrMCzDo/qmhSKNtL71RA2y83vE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\styles\\material-dark.css", "FileLength": 510502, "LastWriteTime": "2025-06-28T04:51:41.3754636+00:00"}, "ACzTtx9S07ymUMd+/N8IDOQJ3JoTXSVz21WAfJUzsV0=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\vfpw31d6oc-ejzuq06h0v.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/material-dark-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\styles\\material-dark-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "tm8db9nnj2", "Integrity": "9UsgcFLNrm087HIQMl6oUb2Wzyn8cgE4Cw7F3qSVm5w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\styles\\material-dark-lite.css", "FileLength": 510505, "LastWriteTime": "2025-06-28T04:51:41.7715842+00:00"}, "tSa8k7Ih65IexyFR/XT56pNtRn1DHmdWGN6L4CX0+kc=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\r3ljs3kien-l25dbew49m.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/material-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\styles\\material-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0tcovrvx8h", "Integrity": "LK7/jJjdg9yqWM07oe+eTDL688trCyNLnf60aWJk/CQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\styles\\material-lite.css", "FileLength": 504057, "LastWriteTime": "2025-06-28T04:51:42.4297606+00:00"}, "cAlga8dCkofxMyR9My1/3W2S7D4gjWSYHt2fFhHwRh0=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\31pjcyl7b8-rxlh8xcfrr.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/material3.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\styles\\material3.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5ecrhboqdu", "Integrity": "fTR9VgzzdZViHzMInBzTTyfF0t0iz6XT3EGj9JdACUk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\styles\\material3.css", "FileLength": 455966, "LastWriteTime": "2025-06-28T04:51:41.8734614+00:00"}, "VuhyrXlORLTutvUKxQVrx2XeqeYqhjNhN5z/Xm5/+Ys=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\6k3aze592a-58b04lxeb2.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/material3-dark.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\styles\\material3-dark.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3rx14l99vh", "Integrity": "HykCI0F865t66nv5XpYo3imUm+Ui5xxgMQCrhdPbRv4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\styles\\material3-dark.css", "FileLength": 455511, "LastWriteTime": "2025-06-28T04:51:42.632215+00:00"}, "V2rH94T1mCWC/dpH9rc20uTp3+GL5pwAmtYkz5vks6w=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\6ypvevihw1-j7l0bwkkcm.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/material3-dark-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\styles\\material3-dark-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bcxttaaq2u", "Integrity": "9ewVOIdEAGrprPea7wzrIePQMoSPU5gyXaSaHKXDt/s=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\styles\\material3-dark-lite.css", "FileLength": 455515, "LastWriteTime": "2025-06-28T04:51:43.0316748+00:00"}, "/xMsth9Hg1+J4NWEgX6gtMzU3lWD1Q+r2oM57sVfmJo=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\a06iirivll-w2jjzrl3tv.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/material3-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\styles\\material3-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3olz3qewzn", "Integrity": "c8MkcVS6dpkI3dEQ6ob1ghIoB7Amn659XggHs7r8SOc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\styles\\material3-lite.css", "FileLength": 455974, "LastWriteTime": "2025-06-28T04:51:43.2669403+00:00"}, "lFcX/yYaNwp/cysoHt6UcXXfK/vafalXfwBmOIbXJ0M=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\6d7zsmg5a9-1q00rsok8r.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/tailwind.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\styles\\tailwind.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "78poa2ihde", "Integrity": "t4c9qnz0hjZmCiDjSE/j9W+w8xL+Rpr0AL1Kpe/SrCI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\styles\\tailwind.css", "FileLength": 438513, "LastWriteTime": "2025-06-28T04:51:43.7233018+00:00"}, "5SzrE4yiIZPcGeoVWXgQg5Ulr8Xw6lYScBuEBry+rg8=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\iy9vurkk9t-1avtkruox7.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/tailwind-dark.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\styles\\tailwind-dark.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fzxjdfne1n", "Integrity": "y+vppyoqsQFY4LZoSJ29QJsGrki5yXhSXcG3ZDNQuPo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\styles\\tailwind-dark.css", "FileLength": 441497, "LastWriteTime": "2025-06-28T04:51:44.213008+00:00"}, "e0D+Ru6Pf3Hp7ValTARqhoRTE6/Jt62Xw3MgLehnTTM=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\hhus5v12qi-03qr4t1egm.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/tailwind-dark-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\styles\\tailwind-dark-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "szeohfo4wh", "Integrity": "6C57jPJHITIJmv18U/U2ht+ydWQeG9R7R2pgGXcuPbY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\styles\\tailwind-dark-lite.css", "FileLength": 441505, "LastWriteTime": "2025-06-28T04:51:44.4750308+00:00"}, "Cv+tstuXvfaIPlsOjRY3qBuB2UWBztVDR3UaJuIPuv0=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\u360ovkwm8-m2qpowulgv.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/tailwind-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\styles\\tailwind-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "g82g2g8fy6", "Integrity": "sM7+4E6xteqKJtffrATgA816G0R/omjwwyej72nj1Wg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\styles\\tailwind-lite.css", "FileLength": 438515, "LastWriteTime": "2025-06-28T04:51:44.6728363+00:00"}, "T+c659ufg+71p3ZxETBBJesEAJ+D6NIjGaQAD+Py9nY=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\clkons3prr-75jc18zxh3.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/tailwind3.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\styles\\tailwind3.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cxixg105kq", "Integrity": "ENKfiv5++Bg6ZKcEOD7UkhIa3UnF62uDTk8AoCW3WCw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\styles\\tailwind3.css", "FileLength": 498779, "LastWriteTime": "2025-06-28T04:51:45.192929+00:00"}, "scEtxzPvTW1qqtnxaD9tepLQHHNC6HCY+Gsg5VxNd7M=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\6zteovpk60-e19qfwtwrl.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/tailwind3-dark.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\styles\\tailwind3-dark.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "o08tb8wvof", "Integrity": "KGfsTMPK5gO/+zMj0BD1hHhi0RvFXfSPqjsI380LNbg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\styles\\tailwind3-dark.css", "FileLength": 496648, "LastWriteTime": "2025-06-28T04:51:45.4105192+00:00"}, "/fTNbirwSAAuTIicAlr8NfidJdxMor32HVgy29RuMWI=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\awup78ll1e-32k2zs5bmi.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/tailwind3-dark-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\styles\\tailwind3-dark-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "14i1oska<PERSON>", "Integrity": "8dmuxFBHkRYfOwon7Vh3qEon50Q18WE7/96KaZRc/dg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\styles\\tailwind3-dark-lite.css", "FileLength": 496651, "LastWriteTime": "2025-06-28T04:51:45.6265527+00:00"}, "PqzO+kmjeRlMDpDVMCCbdy27mfiQD6ObsWT6yIKLkzQ=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\9zslhy3vwt-kuihdgzoxs.gz", "SourceId": "Syncfusion.Blazor", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Syncfusion.Blazor", "RelativePath": "styles/tailwind3-lite.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\styles\\tailwind3-lite.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hg8v4w9558", "Integrity": "kRDlRgL6b7QNJA9cGOaaaFudcIakrEElACyowUVSTv4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\syncfusion.blazor\\30.1.37\\staticwebassets\\styles\\tailwind3-lite.css", "FileLength": 498782, "LastWriteTime": "2025-06-28T04:51:45.8277336+00:00"}, "UqGhyIUkvqyQIc6EKzIVq7bJ4+Fm6Tymov+XDJ5pn4U=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\cdrmjjq1d6-dpoev2zj9b.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Anchor/FluentAnchor.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Anchor\\FluentAnchor.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fo9kun7a2u", "Integrity": "//clW2GxYKMKCQ11fp3GeXX3FIThT7MZFC8x35s69eU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Anchor\\FluentAnchor.razor.js", "FileLength": 267, "LastWriteTime": "2025-06-28T04:51:45.828695+00:00"}, "ExFQm1AFPRu/opJJQjOEVYzI9I2kM/6h9TJmuQDeyAw=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\twd6d0wh4o-819w3ybe2d.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/AnchoredRegion/FluentAnchoredRegion.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\AnchoredRegion\\FluentAnchoredRegion.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "182vbtilql", "Integrity": "Afys6mc7pO9MdyokTQwtIDBaLTcYTTR9veWNwNIktvU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\AnchoredRegion\\FluentAnchoredRegion.razor.js", "FileLength": 1002, "LastWriteTime": "2025-06-28T04:51:45.8317026+00:00"}, "azc4EKm2P1MeDBO3d1gimcCQGsvgezUlv80Ftz6uSkA=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\akzl5bxou0-p6kf5zqzit.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Button/FluentButton.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Button\\FluentButton.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cxryly5d9i", "Integrity": "ILEdH4KtR1Hb7JiJVs56TK2orcZyV4zmIUMwLCiQMaE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Button\\FluentButton.razor.js", "FileLength": 311, "LastWriteTime": "2025-06-28T04:51:45.833741+00:00"}, "kGw1x/eW70oWjBTTY7pXROP9PheGJ95R5Rn8t2ZfC2w=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\cq4bmj8gmb-zjzit57lox.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Checkbox/FluentCheckbox.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Checkbox\\FluentCheckbox.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0aw64dy5f3", "Integrity": "GN9Xra9rtwmLDCgEbgg9cmd8TqFT2hcKzoh25cmNDXU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Checkbox\\FluentCheckbox.razor.js", "FileLength": 217, "LastWriteTime": "2025-06-28T04:51:45.8348187+00:00"}, "VjsOs76nXcHWZdgAn6RVtXaGqskSKv5QuSA8h0P9CSs=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\klh9ieqob8-nfhyg6xvey.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/DataGrid/FluentDataGrid.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\DataGrid\\FluentDataGrid.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7c22o04ryb", "Integrity": "qW8M83Rbn9fAXv7nGXmChE7Qo2htZhG8ALruzktPsU8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\DataGrid\\FluentDataGrid.razor.js", "FileLength": 2962, "LastWriteTime": "2025-06-28T04:51:41.5784887+00:00"}, "MmkQSMHpQurEii9Pnl7qZxjlirNwr/QsVY4NpROrth0=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\4jvn6qpxj5-vyjqmndgy2.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/DesignSystemProvider/FluentDesignTheme.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\DesignSystemProvider\\FluentDesignTheme.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bo7nvuwvlw", "Integrity": "m/2KN+FpPqIvh9wR4E9l87AbfY4hA5AtyFeDpfKxDbc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\DesignSystemProvider\\FluentDesignTheme.razor.js", "FileLength": 754, "LastWriteTime": "2025-06-28T04:51:41.5804894+00:00"}, "X/rLoSOM5K0dVOk3mQPHUYVPZKLaV8KfQtWpz3IKreM=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\7g337ulk4n-iy34mpf72d.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Divider/FluentDivider.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Divider\\FluentDivider.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "glesyzohpx", "Integrity": "5MY4SrjNix5pk9Q5gpyB70xS5vpChA2Ji/0LnyzJwjo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Divider\\FluentDivider.razor.js", "FileLength": 231, "LastWriteTime": "2025-06-28T04:51:41.5824918+00:00"}, "WvzUfOQ7jX+0GY5hQZsst/VbBLkvzlFiHKdUTdb5e4c=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\6mwvvlkmgj-hi1gwvth64.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Grid/FluentGrid.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Grid\\FluentGrid.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9zocec3c57", "Integrity": "i0j3kxAp406ufS3MsxTNvjzVgyRqDwIajgOehqvtu6M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Grid\\FluentGrid.razor.js", "FileLength": 759, "LastWriteTime": "2025-06-28T04:51:41.584487+00:00"}, "8dHjU8XDak3CD+uIy6nZ1QP9pOJuDwP4I1MdDRvgewI=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\pfc9to5k3q-5pcucyxosc.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/HorizontalScroll/FluentHorizontalScroll.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\HorizontalScroll\\FluentHorizontalScroll.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "o73xmx6c30", "Integrity": "hOhymz8Hwrl2LWr7Y0TXXQPcQmpfSCsFogOb6Zv2kB4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\HorizontalScroll\\FluentHorizontalScroll.razor.js", "FileLength": 138, "LastWriteTime": "2025-06-28T04:51:41.5864912+00:00"}, "UMmWHxQ4WyjnmVfjFL1aLWY80wpZ1dbJE1qeAV94hFo=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\h0qxm5u0yy-vjluklws0l.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/InputFile/FluentInputFile.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\InputFile\\FluentInputFile.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1sdzkmuo8x", "Integrity": "gbwptaJwa99wlBJATqA39Wvd+hPAzyivnXV9kSwBbNk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\InputFile\\FluentInputFile.razor.js", "FileLength": 834, "LastWriteTime": "2025-06-28T04:51:41.5884883+00:00"}, "bvydFGwQ/XzvfKuT0+msCYFsKyF53PBFaDkkmQjtVYA=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\win2gt64sq-pu9hn1jugj.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/KeyCode/FluentKeyCode.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\KeyCode\\FluentKeyCode.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "f0dloqnx2v", "Integrity": "4VrmdaAeF/CVMJ7a55YiF2hEI0LDZi84KcPQcvi3ZM4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\KeyCode\\FluentKeyCode.razor.js", "FileLength": 891, "LastWriteTime": "2025-06-28T04:51:41.5904883+00:00"}, "xxNwZhXOtIbb5oKPfpA9NBD/CJd1Z7HBThIlV/2HNyY=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\ijuf810ucb-xp2f0e0rh3.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Label/FluentInputLabel.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Label\\FluentInputLabel.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nmysnspdoa", "Integrity": "T9UdQlDeqtgxKoaOrC15cRB5b8Xw+a6l3AS6Y+XsXL8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Label\\FluentInputLabel.razor.js", "FileLength": 251, "LastWriteTime": "2025-06-28T04:51:41.5924883+00:00"}, "caw1MjCLA9+s8yha16liyqLR6lF+gOBlrtitjRzdBvU=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\6o9gthdpke-psptt994gq.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/List/FluentAutocomplete.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\List\\FluentAutocomplete.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "b9j8p69emz", "Integrity": "gUtYatED8B/J0nKbZybxmXWnL/UJrKEPtENtEKPigwg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\List\\FluentAutocomplete.razor.js", "FileLength": 570, "LastWriteTime": "2025-06-28T04:51:41.057486+00:00"}, "mHmWGSstoQ6E/Q1UqzjCwmdodAS86uQ4do8+dwQYCkk=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\tb98bamshq-afevzs963z.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/List/FluentCombobox.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\List\\FluentCombobox.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "inru90os05", "Integrity": "fhAJBRWOqIUuZBy3gnCpMXRnHWRrtx32KmxhrDCzqLQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\List\\FluentCombobox.razor.js", "FileLength": 544, "LastWriteTime": "2025-06-28T04:51:41.0554863+00:00"}, "7+avK1J1fGsU12q0vMrTSzOFymIywjkohOjq3Ft6m48=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\ffdjjohsnc-mmp1yy7un5.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/List/ListComponentBase.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\List\\ListComponentBase.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "k1s7y80tos", "Integrity": "uEs3SkTKioIAKwT/E2rYx8lRyA3csV0YWEB5VboYALI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\List\\ListComponentBase.razor.js", "FileLength": 137, "LastWriteTime": "2025-06-28T04:51:41.057486+00:00"}, "DPzb0W3VHaU2oSKBlpw1ZTmIkn5IxTJZnUvFYt4XINo=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\pn46o4hdgm-5wrroj4j54.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Menu/FluentMenu.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Menu\\FluentMenu.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "d7qvv8cqi0", "Integrity": "aXVPltYYB1IqzLOOo/ihlIm1QLjjfas+mNS4YtuShCI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Menu\\FluentMenu.razor.js", "FileLength": 298, "LastWriteTime": "2025-06-28T04:51:41.060488+00:00"}, "vMnFXbvO3S6wVJG3tkAf0XEq7N5Z3agIm9El5yaIVQ4=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\4sjayj892b-9fmja7pljs.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/NavMenu/FluentNavMenu.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\NavMenu\\FluentNavMenu.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ch14addp29", "Integrity": "gI4r2CiVSDsh0E4W8EzzQkWVMP1z5KqWsHc5UHSf4lA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\NavMenu\\FluentNavMenu.razor.js", "FileLength": 1012, "LastWriteTime": "2025-06-28T04:51:41.0674566+00:00"}, "DgERrqapI+n/CR9mqOYaYbuqUVmFF1HXbC8bR+w/O6Q=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\mm8wr5jb95-rgycuwl3sw.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Overflow/FluentOverflow.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Overflow\\FluentOverflow.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "88o5cg3t4w", "Integrity": "GLrvqsihiaOKSTpR4HUdwLbyE5ovO/PgdDHABPeQqQ4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Overflow\\FluentOverflow.razor.js", "FileLength": 1755, "LastWriteTime": "2025-06-28T04:51:41.0988853+00:00"}, "HNkjbXvdlS2HLJ6wBS8IROQjcaRahWGN2ky8iwIoyqw=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\wmgko3ir4p-kjm33rwg1a.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Overlay/FluentOverlay.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Overlay\\FluentOverlay.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "s2s2rdqeh5", "Integrity": "XJbbN4OuwYXj7k13JHIv9Lj1t118BSzODet/C6GywE0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Overlay\\FluentOverlay.razor.js", "FileLength": 656, "LastWriteTime": "2025-06-28T04:51:41.0629973+00:00"}, "XGOKyAz/J0UuZM2DYWykPzczhjKMxoCsMP6DsCZKXAk=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\jq2jihf5fr-awzanx0pu8.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/PullToRefresh/FluentPullToRefresh.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\PullToRefresh\\FluentPullToRefresh.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "m5zicgu0uv", "Integrity": "ei4JgX2LTuVjcaPCISvNeX1hHKeJ8uYfuz0+kGtJAkI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\PullToRefresh\\FluentPullToRefresh.razor.js", "FileLength": 1844, "LastWriteTime": "2025-06-28T04:51:41.0853515+00:00"}, "163Qnhj5mcHYHCCt6ax6iEPVMokWSipIx5D8hLO1atc=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\uslingtyva-m0sdc2vg34.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Search/FluentSearch.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Search\\FluentSearch.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "w98asdg00m", "Integrity": "ZDiIU0h55N8SSpI62bLHZqaEzTK3XYMZ7oWs+4kRUmo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Search\\FluentSearch.razor.js", "FileLength": 327, "LastWriteTime": "2025-06-28T04:51:41.0988853+00:00"}, "abRQvOKXO1/ZJucaBLPvisPQIkysLAfQj+uMAKSqWxI=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\2b4v2q74ar-0b0bj86z40.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Slider/FluentSlider.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Slider\\FluentSlider.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "azl6ax9okv", "Integrity": "Ok3+UlpFQuzZXWBv0rF9abeEFcHU9G9/yGNlsDrEjXw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Slider\\FluentSlider.razor.js", "FileLength": 284, "LastWriteTime": "2025-06-28T04:51:41.1368854+00:00"}, "wi/xaxGS8YzyCZ7IsNsYvToMJixRGw5OSwTvXCA4liY=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\5eye7ha8pe-e5lgg05xwp.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Slider/FluentSliderLabel.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Slider\\FluentSliderLabel.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "k78ptnt0os", "Integrity": "MEEF9qZp8vzu++12GS1Nrm9HsAwBidvWQdUYcJ+WJvY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Slider\\FluentSliderLabel.razor.js", "FileLength": 219, "LastWriteTime": "2025-06-28T04:51:41.104885+00:00"}, "Oci9ZRpiVuI7x/V3XZvCVpAFOtrkeDjTnZhkzq5bxDQ=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\fiqznfocyn-ki10xp5gks.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/SortableList/FluentSortableList.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\SortableList\\FluentSortableList.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4tkabjbg0r", "Integrity": "2E4hhc6jOpgtINxrSRMKIqw+DQLQ2SftfcX1ThOrepM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\SortableList\\FluentSortableList.razor.js", "FileLength": 469, "LastWriteTime": "2025-06-28T04:51:41.1078839+00:00"}, "Su3hkYxkW0GBQE+1BBAoaNJCc4fyCcVxPR1q3J3WURY=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\5dcvuemm5a-s9hcthfn4x.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Splitter/FluentMultiSplitter.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Splitter\\FluentMultiSplitter.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mxbb0zqj51", "Integrity": "1iL0Rcjo40DSIIzfew0csjsBypwBYzFxskd2cyvll1c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Splitter\\FluentMultiSplitter.razor.js", "FileLength": 1349, "LastWriteTime": "2025-06-28T04:51:41.1138851+00:00"}, "GxOY2kItPlncHlvMbw8VGLDEAsDh66esRWu6vx3uEpM=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\sc7iw8bo2n-idf8r2y2gj.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Tabs/FluentTab.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Tabs\\FluentTab.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7vrwdrxykv", "Integrity": "7aHGbSJcSaT5cfjQK+Gyrtoyd3MYNywpo1mPbW862yk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Tabs\\FluentTab.razor.js", "FileLength": 293, "LastWriteTime": "2025-06-28T04:51:41.1168851+00:00"}, "qAC+w3Z/qQPtk2Rvor+C/s5PGWPUbgLV6fST/+VDHzA=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\sbcuig79xm-btwuipzwbp.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/TextField/FluentTextField.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\TextField\\FluentTextField.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8ov9jz4ar3", "Integrity": "mCekEzA0zB8lJvQIv6M/2Ndz99hlmD0UE8rEDHgUAps=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\TextField\\FluentTextField.razor.js", "FileLength": 464, "LastWriteTime": "2025-06-28T04:51:41.1178846+00:00"}, "yyFHVXRnzrj9+kC929PIkmhR5LLcEb6zEQaGbNgs/xQ=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\713i7dud0v-v95crb0bvb.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Toolbar/FluentToolbar.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Toolbar\\FluentToolbar.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4ao9gsby09", "Integrity": "1OzW47M+BAzhQZXA66lSbRUQZP1ObnRRM4TTFKNDCh4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Toolbar\\FluentToolbar.razor.js", "FileLength": 513, "LastWriteTime": "2025-06-28T04:51:41.1218854+00:00"}, "QGfLkD10ARwp2A/SXomZppXn8tZMwFe4lkirfDCf/yI=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\is0nail857-b0dyrub9as.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Components/Tooltip/FluentTooltip.razor.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Tooltip\\FluentTooltip.razor.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fg0nienog3", "Integrity": "8ePQzkTFcBOjLSt8PxALnbVMRW1l7AfeMHcGCtC1Jdg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Components\\Tooltip\\FluentTooltip.razor.js", "FileLength": 276, "LastWriteTime": "2025-06-28T04:51:41.1228841+00:00"}, "SeOlP6b8RmQhX7W79sdKv9S1MZx0W3DHhU0aInr28ms=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\6nn42apl8y-1dlotxxwer.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "css/reboot.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\css\\reboot.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2cbdkxfmct", "Integrity": "Xl2r/uacHVD+mrIVAVDC5GAc5mMeiGjzQRAeyrzLwzc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\css\\reboot.css", "FileLength": 2181, "LastWriteTime": "2025-06-28T04:51:41.1288847+00:00"}, "Hrafo2dKvnHXrnIjSsLArrsWQchm73Wn2Y2Gpg7rAUg=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\m1yjzwotev-f8c5bd5212.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "js/initializersLoader.webview.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\js\\initializersLoader.webview.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mnaqae6vjn", "Integrity": "juVE/OQUgf6veO9EE5R7w8sUbTnBaMtqSwEftLHbeM4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\js\\initializersLoader.webview.js", "FileLength": 513, "LastWriteTime": "2025-06-28T04:51:41.1328848+00:00"}, "oRg2fyBgeHfWIROgi+HiGgeBqfqYkmyMHyBp5d9auNY=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\ruk4ibgzyp-t5s4sbrbsi.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "js/loading-theme.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\js\\loading-theme.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rgr9t677zw", "Integrity": "SzheGEdps0ArWi83idGsg6m6WlDMfdv4S5PpD4i+7pE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\js\\loading-theme.js", "FileLength": 1182, "LastWriteTime": "2025-06-28T04:51:41.1418828+00:00"}, "u3hn+HDtjlwlz2KQ/taaONS8z3yWpO8inZT94WZRmDw=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\kwrall9b0s-y92cxfqtgl.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Microsoft.FluentUI.AspNetCore.Components.lib.module.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Microsoft.FluentUI.AspNetCore.Components.lib.module.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3a8h9hbomh", "Integrity": "1X6i8A29INvrhpx2iST6R2pGeSEC9jwBm/t4w6I3TyA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Microsoft.FluentUI.AspNetCore.Components.lib.module.js", "FileLength": 90259, "LastWriteTime": "2025-06-28T04:51:41.2290271+00:00"}, "WVpqmPkhn8RnuJDs2gJLCa0TKrpWpqQeVHPMugOjcqU=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\m3ffqup1h6-kz8gc8cxma.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Microsoft.FluentUI.AspNetCore.Components.lib.module.js.LEGAL.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Microsoft.FluentUI.AspNetCore.Components.lib.module.js.LEGAL.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "t8zskhp41d", "Integrity": "k1bvyfht4MPa+2CGIYyjCqpmHAmcIb02LT50Ty0KClk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Microsoft.FluentUI.AspNetCore.Components.lib.module.js.LEGAL.txt", "FileLength": 568, "LastWriteTime": "2025-06-28T04:51:41.4384765+00:00"}, "TzcIcLd3tyIZmvkQr+D04DImtYJVRaNdcINbMDWt1cc=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\3emkb5z5ma-8j22j5h3b2.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Microsoft.FluentUI.AspNetCore.Components.lib.module.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Microsoft.FluentUI.AspNetCore.Components.lib.module.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "dj93cmo8cl", "Integrity": "94gUrH5jYenrkpJAKcu8Rcc6G3T/PvRHDTVVsp6p+lo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Microsoft.FluentUI.AspNetCore.Components.lib.module.js.map", "FileLength": 278620, "LastWriteTime": "2025-06-28T04:51:41.5224869+00:00"}, "bDEexz/1BFUiSu0eZtD2nWF63EooYENtYIU06JbmC2g=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\rymcp4ogl1-kwazt7t2v0.gz", "SourceId": "JayERP", "SourceType": "Discovered", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/JayERP", "RelativePath": "app#[.{fingerprint=kwazt7t2v0}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\OfficeProjects\\JayERP\\JayERP\\wwwroot\\app.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "aan7m9jccg", "Integrity": "ojvuHjteZKtRkf5ifFya5H1XyrVWrAh8aqMxTizzvCE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\OfficeProjects\\JayERP\\JayERP\\wwwroot\\app.css", "FileLength": 2332, "LastWriteTime": "2025-06-28T04:51:41.5234875+00:00"}, "Zsn1exkMVs1yXaxWp0RlG/hXx1M+7LEIQtdfTjor+9c=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\8j3043sh3g-a8m5cweeeb.gz", "SourceId": "JayERP", "SourceType": "Discovered", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/JayERP", "RelativePath": "favicon#[.{fingerprint=a8m5cweeeb}]?.ico.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\OfficeProjects\\JayERP\\JayERP\\wwwroot\\favicon.ico", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7r53oxhn9o", "Integrity": "40vJMMDtK/I4oykwnLTf7u86lMXa0jAst/FsrWyHFAI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\OfficeProjects\\JayERP\\JayERP\\wwwroot\\favicon.ico", "FileLength": 5357, "LastWriteTime": "2025-06-28T04:51:41.5254874+00:00"}, "U0VrQn0gBjYr9VQBGbL6DintdA0DXfs8Dzt2b3BuAS0=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\u2jq0njg2g-uhfllo7vmv.gz", "SourceId": "JayERP", "SourceType": "Computed", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/JayERP", "RelativePath": "JayERP.modules.json.gz", "AssetKind": "Build", "AssetMode": "CurrentProject", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\jsmodules\\jsmodules.build.manifest.json", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "pmmudzy78n", "Integrity": "EUcPo74MSmhv/hLMcB1hGxjI+dVgHeDs4j3s9WycJ0A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\jsmodules\\jsmodules.build.manifest.json", "FileLength": 93, "LastWriteTime": "2025-06-28T04:51:41.5264876+00:00"}, "COuPtrZC6UO5y6axCDqfH/u1KS9MVRrg3sENol7qzR8=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\5dr5gsymsw-q21vm7bk8w.gz", "SourceId": "Microsoft.FluentUI.AspNetCore.Components", "SourceType": "Package", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.FluentUI.AspNetCore.Components", "RelativePath": "Microsoft.FluentUI.AspNetCore.Components.q21vm7bk8w.bundle.scp.css.gz", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Microsoft.FluentUI.AspNetCore.Components.q21vm7bk8w.bundle.scp.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "v99o1hjjup", "Integrity": "DUYRo3aBXcG5TcO2iCbSCYEify4c77QCT7R4VobttpI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.fluentui.aspnetcore.components\\4.11.5\\staticwebassets\\Microsoft.FluentUI.AspNetCore.Components.q21vm7bk8w.bundle.scp.css", "FileLength": 14001, "LastWriteTime": "2025-06-28T04:51:41.5334866+00:00"}, "VTNlcoXzRiMUGhKii7hOD+GBRUEfPQd/W6FSnlm3CdA=": {"Identity": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\3lx4yccjzd-tnv30r1bl8.gz", "SourceId": "JayERP", "SourceType": "Computed", "ContentRoot": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/JayERP", "RelativePath": "JayERP#[.{fingerprint=tnv30r1bl8}]?.styles.css.gz", "AssetKind": "All", "AssetMode": "CurrentProject", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\scopedcss\\bundle\\JayERP.styles.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5p5exba8qm", "Integrity": "p34461m23NS2B9thc/7tvXKzzhTea6UPLFuDEqULSzM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\OfficeProjects\\JayERP\\JayERP\\obj\\Debug\\net9.0\\scopedcss\\bundle\\JayERP.styles.css", "FileLength": 109, "LastWriteTime": "2025-06-28T04:51:41.5344869+00:00"}}, "CachedCopyCandidates": {}}