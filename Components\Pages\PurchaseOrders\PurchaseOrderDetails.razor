@using JayERP.DTOs
@using JayERP.Services
@using Syncfusion.Blazor.Grids
@using Syncfusion.Blazor.Buttons

@inject IPurchaseOrderService PurchaseOrderService
@inject IJSRuntime JSRuntime

@if (purchaseOrder != null)
{
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Purchase Order Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label fw-bold">Order Number:</label>
                                    <p class="mb-0">@purchaseOrder.OrderNumber</p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label fw-bold">Vendor:</label>
                                    <p class="mb-0">@purchaseOrder.VendorName</p>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label fw-bold">Order Date:</label>
                                    <p class="mb-0">@purchaseOrder.OrderDate.ToString("MMM dd, yyyy")</p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label fw-bold">Expected Delivery:</label>
                                    <p class="mb-0">@(purchaseOrder.ExpectedDeliveryDate?.ToString("MMM dd, yyyy") ?? "Not specified")</p>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label fw-bold">Payment Terms:</label>
                                    <p class="mb-0">@purchaseOrder.PaymentTerms.ToString()</p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label fw-bold">Status:</label>
                                    <span class="badge @GetStatusBadgeClass(purchaseOrder.Status.ToString()) fs-6">
                                        @purchaseOrder.Status.ToString()
                                    </span>
                                </div>
                            </div>
                        </div>

                        @if (!string.IsNullOrEmpty(purchaseOrder.DeliveryAddress))
                        {
                            <div class="row">
                                <div class="col-12">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">Delivery Address:</label>
                                        <p class="mb-0">@purchaseOrder.DeliveryAddress</p>
                                    </div>
                                </div>
                            </div>
                        }

                        @if (!string.IsNullOrEmpty(purchaseOrder.Notes))
                        {
                            <div class="row">
                                <div class="col-12">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">Notes:</label>
                                        <p class="mb-0">@purchaseOrder.Notes</p>
                                    </div>
                                </div>
                            </div>
                        }

                        @if (!string.IsNullOrEmpty(purchaseOrder.ApprovedBy))
                        {
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">Approved By:</label>
                                        <p class="mb-0">@purchaseOrder.ApprovedBy</p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">Approved Date:</label>
                                        <p class="mb-0">@(purchaseOrder.ApprovedDate?.ToString("MMM dd, yyyy") ?? "")</p>
                                    </div>
                                </div>
                            </div>
                        }
                    </div>
                </div>

                <!-- Purchase Order Items -->
                <div class="card mt-3">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Order Items</h5>
                    </div>
                    <div class="card-body">
                        @if (purchaseOrder.Items.Any())
                        {
                            <SfGrid DataSource="@purchaseOrder.Items"
                                    AllowPaging="false"
                                    Height="400px">
                                <GridColumns>
                                    <GridColumn Field="@nameof(PurchaseOrderItemDto.MaterialCode)" HeaderText="Material Code" Width="120"></GridColumn>
                                    <GridColumn Field="@nameof(PurchaseOrderItemDto.MaterialName)" HeaderText="Material Name" Width="200"></GridColumn>
                                    <GridColumn Field="@nameof(PurchaseOrderItemDto.Quantity)" HeaderText="Ordered Qty" Width="100" Format="N2" TextAlign="TextAlign.Right"></GridColumn>
                                    <GridColumn Field="@nameof(PurchaseOrderItemDto.UnitPrice)" HeaderText="Unit Price" Width="120" Format="C2" TextAlign="TextAlign.Right"></GridColumn>
                                    <GridColumn Field="@nameof(PurchaseOrderItemDto.LineTotal)" HeaderText="Line Total" Width="120" Format="C2" TextAlign="TextAlign.Right"></GridColumn>
                                    <GridColumn Field="@nameof(PurchaseOrderItemDto.ReceivedQuantity)" HeaderText="Received Qty" Width="110" Format="N2" TextAlign="TextAlign.Right"></GridColumn>
                                    <GridColumn Field="@nameof(PurchaseOrderItemDto.PendingQuantity)" HeaderText="Pending Qty" Width="110" Format="N2" TextAlign="TextAlign.Right"></GridColumn>
                                    <GridColumn Field="@nameof(PurchaseOrderItemDto.RequiredDate)" HeaderText="Required Date" Width="120" Format="d" Type="ColumnType.Date"></GridColumn>
                                </GridColumns>
                            </SfGrid>
                        }
                        else
                        {
                            <div class="text-center py-4">
                                <p class="text-muted">No items found for this purchase order.</p>
                            </div>
                        }
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                <!-- Order Summary -->
                <div class="card">
                    <div class="card-header">
                        <h6 class="card-title mb-0">Order Summary</h6>
                    </div>
                    <div class="card-body">
                        <div class="d-flex justify-content-between mb-2">
                            <span>Subtotal:</span>
                            <span>@purchaseOrder.SubTotal.ToString("C2")</span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>Tax Amount:</span>
                            <span>@purchaseOrder.TaxAmount.ToString("C2")</span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>Discount:</span>
                            <span>@purchaseOrder.DiscountAmount.ToString("C2")</span>
                        </div>
                        <hr>
                        <div class="d-flex justify-content-between fw-bold fs-5">
                            <span>Total Amount:</span>
                            <span>@purchaseOrder.TotalAmount.ToString("C2")</span>
                        </div>
                    </div>
                </div>

                <!-- Order Status -->
                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="card-title mb-0">Order Status</h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <span class="badge @GetStatusBadgeClass(purchaseOrder.Status.ToString()) fs-6">
                                @purchaseOrder.Status.ToString()
                            </span>
                        </div>
                        
                        <div class="mb-2">
                            <small class="text-muted">Created: @purchaseOrder.CreatedDate.ToString("MMM dd, yyyy")</small>
                        </div>
                        
                        @if (purchaseOrder.ModifiedDate.HasValue)
                        {
                            <div class="mb-2">
                                <small class="text-muted">Modified: @purchaseOrder.ModifiedDate.Value.ToString("MMM dd, yyyy")</small>
                            </div>
                        }
                        
                        <div class="mb-2">
                            <small class="text-muted">Created by: @purchaseOrder.CreatedBy</small>
                        </div>
                        
                        @if (!string.IsNullOrEmpty(purchaseOrder.ModifiedBy))
                        {
                            <div class="mb-2">
                                <small class="text-muted">Modified by: @purchaseOrder.ModifiedBy</small>
                            </div>
                        }
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="card-title mb-0">Quick Actions</h6>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            @if (purchaseOrder.Status == Model.PurchaseOrderStatus.Draft)
                            {
                                <SfButton @onclick="() => ApprovePurchaseOrder()"
                                          CssClass="e-success"
                                          IconCss="e-icons e-check">
                                    Approve Order
                                </SfButton>
                            }
                            
                            @if (purchaseOrder.Status == Model.PurchaseOrderStatus.Approved)
                            {
                                <SfButton @onclick="() => SendPurchaseOrder()"
                                          CssClass="e-primary"
                                          IconCss="e-icons e-send">
                                    Send to Vendor
                                </SfButton>
                            }
                            
                            @if (purchaseOrder.Status == Model.PurchaseOrderStatus.Sent)
                            {
                                <SfButton @onclick="() => CreateGoodsReceipt()"
                                          CssClass="e-info"
                                          IconCss="e-icons e-plus">
                                    Create Goods Receipt
                                </SfButton>
                            }
                            
                            <SfButton @onclick="() => PrintPurchaseOrder()"
                                      CssClass="e-outline"
                                      IconCss="e-icons e-print">
                                Print Order
                            </SfButton>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
}
else if (isLoading)
{
    <div class="text-center p-4">
        <div class="spinner-border" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
        <p class="mt-2">Loading purchase order details...</p>
    </div>
}
else
{
    <div class="text-center p-4">
        <p class="text-muted">Purchase order not found.</p>
    </div>
}

@code {
    [Parameter] public int PurchaseOrderId { get; set; }

    private PurchaseOrderDto? purchaseOrder;
    private bool isLoading = true;

    protected override async Task OnParametersSetAsync()
    {
        if (PurchaseOrderId > 0)
        {
            await LoadPurchaseOrderDetails();
        }
    }

    private async Task LoadPurchaseOrderDetails()
    {
        isLoading = true;
        try
        {
            purchaseOrder = await PurchaseOrderService.GetPurchaseOrderByIdAsync(PurchaseOrderId);
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"Error loading purchase order details: {ex.Message}");
        }
        finally
        {
            isLoading = false;
        }
    }

    private string GetStatusBadgeClass(string status)
    {
        return status switch
        {
            "Draft" => "bg-secondary",
            "PendingApproval" => "bg-warning",
            "Approved" => "bg-success",
            "Sent" => "bg-info",
            "PartiallyReceived" => "bg-primary",
            "FullyReceived" => "bg-success",
            "Cancelled" => "bg-danger",
            "Closed" => "bg-dark",
            _ => "bg-secondary"
        };
    }

    private async Task ApprovePurchaseOrder()
    {
        try
        {
            await PurchaseOrderService.ApprovePurchaseOrderAsync(PurchaseOrderId, "CurrentUser");
            await LoadPurchaseOrderDetails();
            await JSRuntime.InvokeVoidAsync("alert", "Purchase order approved successfully!");
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"Error approving purchase order: {ex.Message}");
        }
    }

    private async Task SendPurchaseOrder()
    {
        await JSRuntime.InvokeVoidAsync("alert", "Send to vendor functionality will be implemented.");
    }

    private async Task CreateGoodsReceipt()
    {
        await JSRuntime.InvokeVoidAsync("alert", "Create goods receipt functionality will be implemented.");
    }

    private async Task PrintPurchaseOrder()
    {
        await JSRuntime.InvokeVoidAsync("alert", "Print functionality will be implemented.");
    }
}
