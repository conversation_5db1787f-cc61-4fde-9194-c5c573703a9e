using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace JayERP.Model
{
    public class ProductionCompletion : BaseEntity
    {
        [Required]
        public int ProductionOrderId { get; set; }
        
        [Range(0, double.MaxValue)]
        public decimal CompletedQuantity { get; set; }
        
        [Range(0, double.MaxValue)]
        public decimal RejectedQuantity { get; set; }
        
        public DateTime CompletionDate { get; set; } = DateTime.UtcNow;
        
        [MaxLength(50)]
        public string BatchNumber { get; set; } = string.Empty;
        
        public DateTime? ExpiryDate { get; set; }
        
        [MaxLength(100)]
        public string StorageLocation { get; set; } = string.Empty;
        
        [MaxLength(100)]
        public string CompletedBy { get; set; } = string.Empty;
        
        [MaxLength(100)]
        public string QualityCheckedBy { get; set; } = string.Empty;
        
        [MaxLength(1000)]
        public string QualityNotes { get; set; } = string.Empty;
        
        [MaxLength(500)]
        public string RejectionReason { get; set; } = string.Empty;
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal ActualCost { get; set; }
        
        [Range(0, double.MaxValue)]
        public decimal YieldPercentage { get; set; }
        
        [MaxLength(1000)]
        public string Notes { get; set; } = string.Empty;
        
        // Navigation Properties
        [ForeignKey("ProductionOrderId")]
        public virtual ProductionOrder ProductionOrder { get; set; } = null!;
    }
}
