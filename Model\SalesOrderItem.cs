using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace JayERP.Model
{
    public class SalesOrderItem : BaseEntity
    {
        [Required]
        public int SalesOrderId { get; set; }
        
        [Required]
        public int ProductId { get; set; }
        
        [Range(0, double.MaxValue)]
        public decimal Quantity { get; set; }
        
        [Range(0, double.MaxValue)]
        public decimal UnitPrice { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal LineTotal { get; set; }
        
        [Range(0, double.MaxValue)]
        public decimal DeliveredQuantity { get; set; }
        
        [Range(0, double.MaxValue)]
        public decimal PendingQuantity { get; set; }
        
        [Range(0, 100)]
        public decimal DiscountPercentage { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal DiscountAmount { get; set; }
        
        [Range(0, 100)]
        public decimal TaxRate { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal TaxAmount { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal NetAmount { get; set; }
        
        public PackagingType PackagingType { get; set; }
        
        [MaxLength(100)]
        public string PackagingDetails { get; set; } = string.Empty;
        
        public DateTime? RequiredDate { get; set; }
        
        [MaxLength(500)]
        public string Notes { get; set; } = string.Empty;
        
        // Navigation Properties
        [ForeignKey("SalesOrderId")]
        public virtual SalesOrder SalesOrder { get; set; } = null!;
        
        [ForeignKey("ProductId")]
        public virtual Product Product { get; set; } = null!;
        
        public virtual ICollection<DeliveryItem> DeliveryItems { get; set; } = new List<DeliveryItem>();
    }
}
