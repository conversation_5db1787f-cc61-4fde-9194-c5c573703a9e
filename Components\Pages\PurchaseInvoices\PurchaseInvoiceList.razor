@page "/purchase-invoices"
@using JayERP.DTOs
@using JayERP.Services
@using JayERP.Model
@using Syncfusion.Blazor.Grids
@using Syncfusion.Blazor.Buttons
@using Syncfusion.Blazor.Inputs
@using Syncfusion.Blazor.DropDowns
@using Syncfusion.Blazor.Popups

@inject IPurchaseInvoiceService PurchaseInvoiceService
@inject IVendorService VendorService
@inject IJSRuntime JSRuntime

<PageTitle>Purchase Invoices</PageTitle>

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h3 class="mb-0">Purchase Invoices</h3>
        <SfButton @onclick="ShowAddPurchaseInvoiceDialog"
                  CssClass="e-primary"
                  IconCss="e-icons e-plus">
            Add Purchase Invoice
        </SfButton>
    </div>

    <!-- Filters Section -->
    <div class="card mb-4">
        <div class="card-body">
            <div class="row g-3">
                <div class="col-md-4">
                    <label class="form-label">Search</label>
                    <SfTextBox @bind-Value="searchText"
                              Placeholder="Search by invoice number, vendor..."
                              ShowClearButton="true"
                              @onchange="OnSearchChanged">
                    </SfTextBox>
                </div>
                <div class="col-md-3">
                    <label class="form-label">Status</label>
                    <SfDropDownList TValue="InvoiceStatus?" TItem="StatusOption"
                                   @bind-Value="selectedStatus"
                                   DataSource="statusOptions"
                                   Placeholder="All Statuses"
                                   AllowClear="true">
                        <DropDownListFieldSettings Text="Text" Value="Value"></DropDownListFieldSettings>
                        <DropDownListEvents TValue="InvoiceStatus?" TItem="StatusOption" ValueChange="OnStatusChanged"></DropDownListEvents>
                    </SfDropDownList>
                </div>
                <div class="col-md-3">
                    <label class="form-label">Date Range</label>
                    <SfTextBox Value="All Dates" Readonly="true"></SfTextBox>
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <SfButton @onclick="ClearFilters"
                              CssClass="e-outline"
                              IconCss="e-icons e-clear-all">
                        Clear
                    </SfButton>
                </div>
            </div>
        </div>
    </div>

    <div class="grid-container">
        <SfGrid DataSource="@purchaseInvoices"
                AllowPaging="true"
                AllowSorting="true"
                AllowFiltering="false"
                Height="calc(100vh - 330px)">
            <GridPageSettings PageSize="20"></GridPageSettings>
            <GridColumns>
                <GridColumn Field="@nameof(PurchaseInvoiceListDto.InvoiceNumber)" HeaderText="Invoice Number" Width="120"></GridColumn>
                <GridColumn Field="@nameof(PurchaseInvoiceListDto.VendorName)" HeaderText="Vendor" Width="150"></GridColumn>
                <GridColumn Field="@nameof(PurchaseInvoiceListDto.VendorInvoiceNumber)" HeaderText="Vendor Invoice" Width="130"></GridColumn>
                <GridColumn Field="@nameof(PurchaseInvoiceListDto.InvoiceDate)" HeaderText="Invoice Date" Width="120" Format="d" Type="ColumnType.Date"></GridColumn>
                <GridColumn Field="@nameof(PurchaseInvoiceListDto.DueDate)" HeaderText="Due Date" Width="120" Format="d" Type="ColumnType.Date"></GridColumn>
                <GridColumn Field="@nameof(PurchaseInvoiceListDto.TotalAmount)" HeaderText="Total Amount" Width="120" Format="C2" TextAlign="TextAlign.Right"></GridColumn>
                <GridColumn Field="@nameof(PurchaseInvoiceListDto.PaidAmount)" HeaderText="Paid Amount" Width="120" Format="C2" TextAlign="TextAlign.Right"></GridColumn>
                <GridColumn Field="@nameof(PurchaseInvoiceListDto.BalanceAmount)" HeaderText="Balance" Width="120" Format="C2" TextAlign="TextAlign.Right"></GridColumn>
                <GridColumn Field="@nameof(PurchaseInvoiceListDto.ItemCount)" HeaderText="Items" Width="80" TextAlign="TextAlign.Center"></GridColumn>
                <GridColumn Field="@nameof(PurchaseInvoiceListDto.Status)" HeaderText="Status" Width="120" TextAlign="TextAlign.Center">
                    <Template>
                        @{
                            var invoiceItem = (context as PurchaseInvoiceListDto);
                            var badgeClass = GetStatusBadgeClass(invoiceItem.Status);
                        }
                        <span class="badge @badgeClass">
                            @invoiceItem.Status
                        </span>
                    </Template>
                </GridColumn>
                <GridColumn HeaderText="Actions" Width="150" TextAlign="TextAlign.Center">
                    <Template>
                        @{
                            var invoiceItem = (context as PurchaseInvoiceListDto);
                        }
                        <div class="btn-group" role="group">
                            <SfButton @onclick="() => ViewPurchaseInvoice(invoiceItem.Id)"
                                     CssClass="e-small e-info"
                                     IconCss="e-icons e-eye">
                            </SfButton>
                            <SfButton @onclick="() => EditPurchaseInvoice(invoiceItem.Id)"
                                     CssClass="e-small e-warning"
                                     IconCss="e-icons e-edit">
                            </SfButton>
                            <SfButton @onclick="() => DeletePurchaseInvoice(invoiceItem.Id)"
                                     CssClass="e-small e-danger"
                                     IconCss="e-icons e-delete">
                            </SfButton>
                        </div>
                    </Template>
                </GridColumn>
            </GridColumns>
        </SfGrid>
    </div>
</div>

<!-- Purchase Invoice Form Dialog -->
<SfDialog @bind-Visible="showPurchaseInvoiceDialog"
          Header="@dialogTitle"
          Width="90%"
          Height="80%"
          IsModal="true"
          ShowCloseIcon="true"
          AllowDragging="true">
    <DialogEvents OnOpen="OnDialogOpen"></DialogEvents>
    <ContentTemplate>
        <PurchaseInvoiceForm @ref="purchaseInvoiceForm"
                            PurchaseInvoice="currentPurchaseInvoice"
                            OnSave="OnPurchaseInvoiceSaved"
                            OnCancel="OnPurchaseInvoiceCancelled" />
    </ContentTemplate>
</SfDialog>

<!-- Purchase Invoice Details Dialog -->
<SfDialog @bind-Visible="showPurchaseInvoiceDetailsDialog"
          Header="Purchase Invoice Details"
          Width="90%"
          Height="80%"
          IsModal="true"
          ShowCloseIcon="true"
          AllowDragging="true">
    <ContentTemplate>
        <PurchaseInvoiceDetails @ref="purchaseInvoiceDetails"
                               PurchaseInvoiceId="selectedPurchaseInvoiceId" />
    </ContentTemplate>
</SfDialog>

@code {
    private PurchaseInvoiceForm? purchaseInvoiceForm;
    private PurchaseInvoiceDetails? purchaseInvoiceDetails;

    private List<PurchaseInvoiceListDto> purchaseInvoices = new();
    private List<PurchaseInvoiceListDto> allPurchaseInvoices = new();
    private string searchText = string.Empty;
    private InvoiceStatus? selectedStatus;
    private bool showPurchaseInvoiceDialog = false;
    private bool showPurchaseInvoiceDetailsDialog = false;
    private string dialogTitle = string.Empty;
    private PurchaseInvoiceDto? currentPurchaseInvoice;
    private int selectedPurchaseInvoiceId;

    private List<StatusOption> statusOptions = new()
    {
        new StatusOption { Text = "All Statuses", Value = null },
        new StatusOption { Text = "Draft", Value = InvoiceStatus.Draft },
        new StatusOption { Text = "Sent", Value = InvoiceStatus.Sent },
        new StatusOption { Text = "Paid", Value = InvoiceStatus.Paid },
        new StatusOption { Text = "Partially Paid", Value = InvoiceStatus.PartiallyPaid },
        new StatusOption { Text = "Overdue", Value = InvoiceStatus.Overdue },
        new StatusOption { Text = "Cancelled", Value = InvoiceStatus.Cancelled },
        new StatusOption { Text = "Refunded", Value = InvoiceStatus.Refunded }
    };

    protected override async Task OnInitializedAsync()
    {
        await LoadPurchaseInvoices();
    }

    private async Task LoadPurchaseInvoices()
    {
        try
        {
            allPurchaseInvoices = (await PurchaseInvoiceService.GetAllPurchaseInvoicesAsync()).ToList();
            ApplyFilters();
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"Error loading purchase invoices: {ex.Message}");
        }
    }

    private void ApplyFilters()
    {
        var filtered = allPurchaseInvoices.AsEnumerable();

        if (!string.IsNullOrWhiteSpace(searchText))
        {
            filtered = filtered.Where(pi => 
                pi.InvoiceNumber.Contains(searchText, StringComparison.OrdinalIgnoreCase) ||
                pi.VendorName.Contains(searchText, StringComparison.OrdinalIgnoreCase) ||
                pi.VendorInvoiceNumber.Contains(searchText, StringComparison.OrdinalIgnoreCase));
        }

        if (selectedStatus.HasValue)
        {
            filtered = filtered.Where(pi => pi.Status == selectedStatus.Value.ToString());
        }

        purchaseInvoices = filtered.ToList();
    }

    private void OnSearchChanged()
    {
        ApplyFilters();
    }

    private void OnStatusChanged(ChangeEventArgs<InvoiceStatus?, StatusOption> args)
    {
        selectedStatus = args.Value;
        ApplyFilters();
    }

    private void ClearFilters()
    {
        searchText = string.Empty;
        selectedStatus = null;
        ApplyFilters();
    }

    private void ShowAddPurchaseInvoiceDialog()
    {
        dialogTitle = "Add New Purchase Invoice";
        currentPurchaseInvoice = null;
        showPurchaseInvoiceDialog = true;
    }

    private async Task EditPurchaseInvoice(int purchaseInvoiceId)
    {
        try
        {
            currentPurchaseInvoice = await PurchaseInvoiceService.GetPurchaseInvoiceByIdAsync(purchaseInvoiceId);
            if (currentPurchaseInvoice != null)
            {
                dialogTitle = "Edit Purchase Invoice";
                showPurchaseInvoiceDialog = true;
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"Error loading purchase invoice: {ex.Message}");
        }
    }

    private async Task ViewPurchaseInvoice(int purchaseInvoiceId)
    {
        selectedPurchaseInvoiceId = purchaseInvoiceId;
        showPurchaseInvoiceDetailsDialog = true;
    }

    private async Task DeletePurchaseInvoice(int purchaseInvoiceId)
    {
        var confirmed = await JSRuntime.InvokeAsync<bool>("confirm", "Are you sure you want to delete this purchase invoice?");
        if (confirmed)
        {
            try
            {
                await PurchaseInvoiceService.DeletePurchaseInvoiceAsync(purchaseInvoiceId);
                await LoadPurchaseInvoices();
                await JSRuntime.InvokeVoidAsync("alert", "Purchase invoice deleted successfully!");
            }
            catch (Exception ex)
            {
                await JSRuntime.InvokeVoidAsync("alert", $"Error deleting purchase invoice: {ex.Message}");
            }
        }
    }

    private async Task OnPurchaseInvoiceSaved()
    {
        showPurchaseInvoiceDialog = false;
        await LoadPurchaseInvoices();
    }

    private void OnPurchaseInvoiceCancelled()
    {
        showPurchaseInvoiceDialog = false;
    }

    private void OnDialogOpen()
    {
        // Dialog opened
    }

    private string GetStatusBadgeClass(string status)
    {
        return status switch
        {
            "Draft" => "bg-secondary",
            "Sent" => "bg-info",
            "Paid" => "bg-success",
            "PartiallyPaid" => "bg-warning",
            "Overdue" => "bg-danger",
            "Cancelled" => "bg-dark",
            "Refunded" => "bg-primary",
            _ => "bg-secondary"
        };
    }

    public class StatusOption
    {
        public string Text { get; set; } = string.Empty;
        public InvoiceStatus? Value { get; set; }
    }
}
