using JayERP.Data;
using JayERP.DTOs;
using JayERP.Model;
using Microsoft.EntityFrameworkCore;

namespace JayERP.Services
{
    public class GoodsReceiptService : IGoodsReceiptService
    {
        private readonly ApplicationDbContext _context;

        public GoodsReceiptService(ApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<IEnumerable<GoodsReceiptListDto>> GetAllGoodsReceiptsAsync()
        {
            return await _context.GoodsReceipts
                .Include(gr => gr.PurchaseOrder)
                .Include(gr => gr.Vendor)
                .Include(gr => gr.Items)
                .Where(gr => gr.IsActive)
                .Select(gr => new GoodsReceiptListDto
                {
                    Id = gr.Id,
                    ReceiptNumber = gr.ReceiptNumber,
                    PurchaseOrderNumber = gr.PurchaseOrder.OrderNumber,
                    VendorName = gr.Vendor.Name,
                    ReceiptDate = gr.ReceiptDate,
                    Status = gr.Status.ToString(),
                    VendorInvoiceNumber = gr.VendorInvoiceNumber,
                    ReceivedBy = gr.ReceivedBy,
                    TotalAmount = gr.TotalAmount,
                    CreatedDate = gr.CreatedDate,
                    ItemCount = gr.Items.Count
                })
                .OrderByDescending(gr => gr.CreatedDate)
                .ToListAsync();
        }

        public async Task<GoodsReceiptDto?> GetGoodsReceiptByIdAsync(int id)
        {
            var goodsReceipt = await _context.GoodsReceipts
                .Include(gr => gr.PurchaseOrder)
                .Include(gr => gr.Vendor)
                .Include(gr => gr.Items)
                    .ThenInclude(item => item.Material)
                .Include(gr => gr.Items)
                    .ThenInclude(item => item.PurchaseOrderItem)
                .FirstOrDefaultAsync(gr => gr.Id == id && gr.IsActive);

            if (goodsReceipt == null) return null;

            return new GoodsReceiptDto
            {
                Id = goodsReceipt.Id,
                ReceiptNumber = goodsReceipt.ReceiptNumber,
                PurchaseOrderId = goodsReceipt.PurchaseOrderId,
                PurchaseOrderNumber = goodsReceipt.PurchaseOrder.OrderNumber,
                VendorId = goodsReceipt.VendorId,
                VendorName = goodsReceipt.Vendor.Name,
                ReceiptDate = goodsReceipt.ReceiptDate,
                Status = goodsReceipt.Status,
                VendorInvoiceNumber = goodsReceipt.VendorInvoiceNumber,
                VendorInvoiceDate = goodsReceipt.VendorInvoiceDate,
                DeliveryNoteNumber = goodsReceipt.DeliveryNoteNumber,
                ReceivedBy = goodsReceipt.ReceivedBy,
                InspectedBy = goodsReceipt.InspectedBy,
                Notes = goodsReceipt.Notes,
                TotalAmount = goodsReceipt.TotalAmount,
                CreatedDate = goodsReceipt.CreatedDate,
                CreatedBy = goodsReceipt.CreatedBy,
                Items = goodsReceipt.Items.Select(item => new GoodsReceiptItemDto
                {
                    Id = item.Id,
                    GoodsReceiptId = item.GoodsReceiptId,
                    PurchaseOrderItemId = item.PurchaseOrderItemId,
                    MaterialId = item.MaterialId,
                    MaterialName = item.Material.Name,
                    MaterialCode = item.Material.Code,
                    OrderedQuantity = item.OrderedQuantity,
                    ReceivedQuantity = item.ReceivedQuantity,
                    AcceptedQuantity = item.AcceptedQuantity,
                    RejectedQuantity = item.RejectedQuantity,
                    UnitPrice = item.UnitPrice,
                    LineTotal = item.LineTotal,
                    BatchNumber = item.BatchNumber,
                    ExpiryDate = item.ExpiryDate,
                    StorageLocation = item.StorageLocation,
                    QualityNotes = item.QualityNotes,
                    RejectionReason = item.RejectionReason
                }).ToList()
            };
        }

        public async Task<GoodsReceiptDto> CreateGoodsReceiptAsync(GoodsReceiptCreateDto createDto, string createdBy)
        {
            var purchaseOrder = await _context.PurchaseOrders
                .Include(po => po.Vendor)
                .FirstOrDefaultAsync(po => po.Id == createDto.PurchaseOrderId);

            if (purchaseOrder == null)
                throw new ArgumentException("Purchase order not found");

            var goodsReceipt = new GoodsReceipt
            {
                ReceiptNumber = await GenerateReceiptNumberAsync(),
                PurchaseOrderId = createDto.PurchaseOrderId,
                VendorId = purchaseOrder.VendorId,
                ReceiptDate = createDto.ReceiptDate,
                Status = createDto.Status,
                VendorInvoiceNumber = createDto.VendorInvoiceNumber,
                VendorInvoiceDate = createDto.VendorInvoiceDate,
                DeliveryNoteNumber = createDto.DeliveryNoteNumber,
                ReceivedBy = createDto.ReceivedBy,
                InspectedBy = createDto.InspectedBy,
                Notes = createDto.Notes,
                CreatedBy = createdBy,
                CreatedDate = DateTime.UtcNow,
                IsActive = true
            };

            _context.GoodsReceipts.Add(goodsReceipt);
            await _context.SaveChangesAsync();

            // Add items
            foreach (var itemDto in createDto.Items)
            {
                var item = new GoodsReceiptItem
                {
                    GoodsReceiptId = goodsReceipt.Id,
                    PurchaseOrderItemId = itemDto.PurchaseOrderItemId,
                    MaterialId = await GetMaterialIdFromPurchaseOrderItemAsync(itemDto.PurchaseOrderItemId),
                    OrderedQuantity = await GetOrderedQuantityAsync(itemDto.PurchaseOrderItemId),
                    ReceivedQuantity = itemDto.ReceivedQuantity,
                    AcceptedQuantity = itemDto.AcceptedQuantity,
                    RejectedQuantity = itemDto.RejectedQuantity,
                    UnitPrice = await GetUnitPriceAsync(itemDto.PurchaseOrderItemId),
                    LineTotal = itemDto.AcceptedQuantity * await GetUnitPriceAsync(itemDto.PurchaseOrderItemId),
                    BatchNumber = itemDto.BatchNumber,
                    ExpiryDate = itemDto.ExpiryDate,
                    StorageLocation = itemDto.StorageLocation,
                    QualityNotes = itemDto.QualityNotes,
                    RejectionReason = itemDto.RejectionReason,
                    CreatedBy = createdBy,
                    CreatedDate = DateTime.UtcNow,
                    IsActive = true
                };

                _context.GoodsReceiptItems.Add(item);
            }

            // Calculate total amount
            goodsReceipt.TotalAmount = createDto.Items.Sum(item => 
                item.AcceptedQuantity * GetUnitPriceAsync(item.PurchaseOrderItemId).Result);

            await _context.SaveChangesAsync();

            return await GetGoodsReceiptByIdAsync(goodsReceipt.Id) ?? throw new InvalidOperationException("Failed to retrieve created goods receipt");
        }

        public async Task<GoodsReceiptDto> UpdateGoodsReceiptAsync(GoodsReceiptUpdateDto updateDto, string modifiedBy)
        {
            var goodsReceipt = await _context.GoodsReceipts
                .Include(gr => gr.Items)
                .FirstOrDefaultAsync(gr => gr.Id == updateDto.Id && gr.IsActive);

            if (goodsReceipt == null)
                throw new ArgumentException("Goods receipt not found");

            // Update goods receipt properties
            goodsReceipt.ReceiptDate = updateDto.ReceiptDate;
            goodsReceipt.Status = updateDto.Status;
            goodsReceipt.VendorInvoiceNumber = updateDto.VendorInvoiceNumber;
            goodsReceipt.VendorInvoiceDate = updateDto.VendorInvoiceDate;
            goodsReceipt.DeliveryNoteNumber = updateDto.DeliveryNoteNumber;
            goodsReceipt.ReceivedBy = updateDto.ReceivedBy;
            goodsReceipt.InspectedBy = updateDto.InspectedBy;
            goodsReceipt.Notes = updateDto.Notes;
            goodsReceipt.TotalAmount = updateDto.TotalAmount;
            goodsReceipt.ModifiedBy = modifiedBy;
            goodsReceipt.ModifiedDate = DateTime.UtcNow;

            // Remove existing items
            _context.GoodsReceiptItems.RemoveRange(goodsReceipt.Items);

            // Add updated items
            foreach (var itemDto in updateDto.Items)
            {
                var item = new GoodsReceiptItem
                {
                    GoodsReceiptId = goodsReceipt.Id,
                    PurchaseOrderItemId = itemDto.PurchaseOrderItemId,
                    MaterialId = await GetMaterialIdFromPurchaseOrderItemAsync(itemDto.PurchaseOrderItemId),
                    OrderedQuantity = await GetOrderedQuantityAsync(itemDto.PurchaseOrderItemId),
                    ReceivedQuantity = itemDto.ReceivedQuantity,
                    AcceptedQuantity = itemDto.AcceptedQuantity,
                    RejectedQuantity = itemDto.RejectedQuantity,
                    UnitPrice = await GetUnitPriceAsync(itemDto.PurchaseOrderItemId),
                    LineTotal = itemDto.AcceptedQuantity * await GetUnitPriceAsync(itemDto.PurchaseOrderItemId),
                    BatchNumber = itemDto.BatchNumber,
                    ExpiryDate = itemDto.ExpiryDate,
                    StorageLocation = itemDto.StorageLocation,
                    QualityNotes = itemDto.QualityNotes,
                    RejectionReason = itemDto.RejectionReason,
                    CreatedBy = goodsReceipt.CreatedBy,
                    CreatedDate = goodsReceipt.CreatedDate,
                    ModifiedBy = modifiedBy,
                    ModifiedDate = DateTime.UtcNow,
                    IsActive = true
                };

                _context.GoodsReceiptItems.Add(item);
            }

            await _context.SaveChangesAsync();

            return await GetGoodsReceiptByIdAsync(goodsReceipt.Id) ?? throw new InvalidOperationException("Failed to retrieve updated goods receipt");
        }

        public async Task<bool> DeleteGoodsReceiptAsync(int id)
        {
            var goodsReceipt = await _context.GoodsReceipts.FindAsync(id);
            if (goodsReceipt == null) return false;

            goodsReceipt.IsActive = false;
            goodsReceipt.ModifiedDate = DateTime.UtcNow;

            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> CompleteGoodsReceiptAsync(int id, string completedBy)
        {
            var goodsReceipt = await _context.GoodsReceipts.FindAsync(id);
            if (goodsReceipt == null) return false;

            goodsReceipt.Status = GoodsReceiptStatus.FullyReceived;
            goodsReceipt.ModifiedBy = completedBy;
            goodsReceipt.ModifiedDate = DateTime.UtcNow;

            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> RejectGoodsReceiptAsync(int id, string rejectedBy, string reason)
        {
            var goodsReceipt = await _context.GoodsReceipts.FindAsync(id);
            if (goodsReceipt == null) return false;

            goodsReceipt.Status = GoodsReceiptStatus.Rejected;
            goodsReceipt.Notes = $"{goodsReceipt.Notes}\nRejected: {reason}";
            goodsReceipt.ModifiedBy = rejectedBy;
            goodsReceipt.ModifiedDate = DateTime.UtcNow;

            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<IEnumerable<GoodsReceiptListDto>> GetGoodsReceiptsByVendorAsync(int vendorId)
        {
            return await _context.GoodsReceipts
                .Include(gr => gr.PurchaseOrder)
                .Include(gr => gr.Vendor)
                .Include(gr => gr.Items)
                .Where(gr => gr.VendorId == vendorId && gr.IsActive)
                .Select(gr => new GoodsReceiptListDto
                {
                    Id = gr.Id,
                    ReceiptNumber = gr.ReceiptNumber,
                    PurchaseOrderNumber = gr.PurchaseOrder.OrderNumber,
                    VendorName = gr.Vendor.Name,
                    ReceiptDate = gr.ReceiptDate,
                    Status = gr.Status.ToString(),
                    VendorInvoiceNumber = gr.VendorInvoiceNumber,
                    ReceivedBy = gr.ReceivedBy,
                    TotalAmount = gr.TotalAmount,
                    CreatedDate = gr.CreatedDate,
                    ItemCount = gr.Items.Count
                })
                .OrderByDescending(gr => gr.CreatedDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<GoodsReceiptListDto>> GetGoodsReceiptsByPurchaseOrderAsync(int purchaseOrderId)
        {
            return await _context.GoodsReceipts
                .Include(gr => gr.PurchaseOrder)
                .Include(gr => gr.Vendor)
                .Include(gr => gr.Items)
                .Where(gr => gr.PurchaseOrderId == purchaseOrderId && gr.IsActive)
                .Select(gr => new GoodsReceiptListDto
                {
                    Id = gr.Id,
                    ReceiptNumber = gr.ReceiptNumber,
                    PurchaseOrderNumber = gr.PurchaseOrder.OrderNumber,
                    VendorName = gr.Vendor.Name,
                    ReceiptDate = gr.ReceiptDate,
                    Status = gr.Status.ToString(),
                    VendorInvoiceNumber = gr.VendorInvoiceNumber,
                    ReceivedBy = gr.ReceivedBy,
                    TotalAmount = gr.TotalAmount,
                    CreatedDate = gr.CreatedDate,
                    ItemCount = gr.Items.Count
                })
                .OrderByDescending(gr => gr.CreatedDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<GoodsReceiptListDto>> GetGoodsReceiptsByStatusAsync(GoodsReceiptStatus status)
        {
            return await _context.GoodsReceipts
                .Include(gr => gr.PurchaseOrder)
                .Include(gr => gr.Vendor)
                .Include(gr => gr.Items)
                .Where(gr => gr.Status == status && gr.IsActive)
                .Select(gr => new GoodsReceiptListDto
                {
                    Id = gr.Id,
                    ReceiptNumber = gr.ReceiptNumber,
                    PurchaseOrderNumber = gr.PurchaseOrder.OrderNumber,
                    VendorName = gr.Vendor.Name,
                    ReceiptDate = gr.ReceiptDate,
                    Status = gr.Status.ToString(),
                    VendorInvoiceNumber = gr.VendorInvoiceNumber,
                    ReceivedBy = gr.ReceivedBy,
                    TotalAmount = gr.TotalAmount,
                    CreatedDate = gr.CreatedDate,
                    ItemCount = gr.Items.Count
                })
                .OrderByDescending(gr => gr.CreatedDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<GoodsReceiptListDto>> SearchGoodsReceiptsAsync(string searchTerm)
        {
            return await _context.GoodsReceipts
                .Include(gr => gr.PurchaseOrder)
                .Include(gr => gr.Vendor)
                .Include(gr => gr.Items)
                .Where(gr => gr.IsActive && (
                    gr.ReceiptNumber.Contains(searchTerm) ||
                    gr.PurchaseOrder.OrderNumber.Contains(searchTerm) ||
                    gr.Vendor.Name.Contains(searchTerm) ||
                    gr.VendorInvoiceNumber.Contains(searchTerm)))
                .Select(gr => new GoodsReceiptListDto
                {
                    Id = gr.Id,
                    ReceiptNumber = gr.ReceiptNumber,
                    PurchaseOrderNumber = gr.PurchaseOrder.OrderNumber,
                    VendorName = gr.Vendor.Name,
                    ReceiptDate = gr.ReceiptDate,
                    Status = gr.Status.ToString(),
                    VendorInvoiceNumber = gr.VendorInvoiceNumber,
                    ReceivedBy = gr.ReceivedBy,
                    TotalAmount = gr.TotalAmount,
                    CreatedDate = gr.CreatedDate,
                    ItemCount = gr.Items.Count
                })
                .OrderByDescending(gr => gr.CreatedDate)
                .ToListAsync();
        }

        // Helper methods and remaining implementations will be added in the next part
        private async Task<int> GetMaterialIdFromPurchaseOrderItemAsync(int purchaseOrderItemId)
        {
            var item = await _context.PurchaseOrderItems.FindAsync(purchaseOrderItemId);
            return item?.MaterialId ?? 0;
        }

        private async Task<decimal> GetOrderedQuantityAsync(int purchaseOrderItemId)
        {
            var item = await _context.PurchaseOrderItems.FindAsync(purchaseOrderItemId);
            return item?.Quantity ?? 0;
        }

        private async Task<decimal> GetUnitPriceAsync(int purchaseOrderItemId)
        {
            var item = await _context.PurchaseOrderItems.FindAsync(purchaseOrderItemId);
            return item?.UnitPrice ?? 0;
        }

        public async Task<string> GenerateReceiptNumberAsync()
        {
            var lastReceipt = await _context.GoodsReceipts
                .OrderByDescending(gr => gr.Id)
                .FirstOrDefaultAsync();

            var nextNumber = (lastReceipt?.Id ?? 0) + 1;
            return $"GR{DateTime.Now:yyyyMM}{nextNumber:D4}";
        }

        public async Task<bool> CanDeleteGoodsReceiptAsync(int id)
        {
            var goodsReceipt = await _context.GoodsReceipts.FindAsync(id);
            return goodsReceipt?.Status == GoodsReceiptStatus.Pending;
        }

        public async Task<bool> CanEditGoodsReceiptAsync(int id)
        {
            var goodsReceipt = await _context.GoodsReceipts.FindAsync(id);
            return goodsReceipt?.Status == GoodsReceiptStatus.Pending;
        }

        public async Task<bool> GoodsReceiptExistsAsync(int id)
        {
            return await _context.GoodsReceipts.AnyAsync(gr => gr.Id == id && gr.IsActive);
        }

        public async Task<IEnumerable<PendingReceiptDto>> GetPendingReceiptsAsync()
        {
            return await _context.PurchaseOrders
                .Include(po => po.Vendor)
                .Include(po => po.Items)
                    .ThenInclude(item => item.Material)
                .Where(po => po.Status == PurchaseOrderStatus.Sent && po.IsActive)
                .Select(po => new PendingReceiptDto
                {
                    PurchaseOrderId = po.Id,
                    PurchaseOrderNumber = po.OrderNumber,
                    VendorName = po.Vendor.Name,
                    OrderDate = po.OrderDate,
                    ExpectedDeliveryDate = po.ExpectedDeliveryDate,
                    TotalAmount = po.TotalAmount,
                    PendingItemsCount = po.Items.Count(item => item.PendingQuantity > 0),
                    PendingItems = po.Items
                        .Where(item => item.PendingQuantity > 0)
                        .Select(item => new PendingReceiptItemDto
                        {
                            PurchaseOrderItemId = item.Id,
                            MaterialId = item.MaterialId,
                            MaterialName = item.Material.Name,
                            MaterialCode = item.Material.Code,
                            OrderedQuantity = item.Quantity,
                            ReceivedQuantity = item.ReceivedQuantity,
                            PendingQuantity = item.PendingQuantity,
                            UnitPrice = item.UnitPrice,
                            RequiredDate = item.RequiredDate
                        }).ToList()
                })
                .ToListAsync();
        }

        public async Task<PendingReceiptDto?> GetPendingReceiptByPurchaseOrderAsync(int purchaseOrderId)
        {
            var purchaseOrder = await _context.PurchaseOrders
                .Include(po => po.Vendor)
                .Include(po => po.Items)
                    .ThenInclude(item => item.Material)
                .FirstOrDefaultAsync(po => po.Id == purchaseOrderId && po.IsActive);

            if (purchaseOrder == null) return null;

            return new PendingReceiptDto
            {
                PurchaseOrderId = purchaseOrder.Id,
                PurchaseOrderNumber = purchaseOrder.OrderNumber,
                VendorName = purchaseOrder.Vendor.Name,
                OrderDate = purchaseOrder.OrderDate,
                ExpectedDeliveryDate = purchaseOrder.ExpectedDeliveryDate,
                TotalAmount = purchaseOrder.TotalAmount,
                PendingItemsCount = purchaseOrder.Items.Count(item => item.PendingQuantity > 0),
                PendingItems = purchaseOrder.Items
                    .Where(item => item.PendingQuantity > 0)
                    .Select(item => new PendingReceiptItemDto
                    {
                        PurchaseOrderItemId = item.Id,
                        MaterialId = item.MaterialId,
                        MaterialName = item.Material.Name,
                        MaterialCode = item.Material.Code,
                        OrderedQuantity = item.Quantity,
                        ReceivedQuantity = item.ReceivedQuantity,
                        PendingQuantity = item.PendingQuantity,
                        UnitPrice = item.UnitPrice,
                        RequiredDate = item.RequiredDate
                    }).ToList()
            };
        }

        public async Task<GoodsReceiptSummaryDto> GetGoodsReceiptSummaryAsync()
        {
            var totalReceipts = await _context.GoodsReceipts.CountAsync(gr => gr.IsActive);
            var pendingReceipts = await _context.GoodsReceipts.CountAsync(gr => gr.Status == GoodsReceiptStatus.Pending && gr.IsActive);
            var completedReceipts = await _context.GoodsReceipts.CountAsync(gr => gr.Status == GoodsReceiptStatus.FullyReceived && gr.IsActive);
            var totalValue = await _context.GoodsReceipts.Where(gr => gr.IsActive).SumAsync(gr => gr.TotalAmount);
            var qualityIssues = await _context.GoodsReceiptItems.CountAsync(gri => gri.RejectedQuantity > 0 && gri.IsActive);

            return new GoodsReceiptSummaryDto
            {
                TotalReceipts = totalReceipts,
                PendingReceipts = pendingReceipts,
                CompletedReceipts = completedReceipts,
                TotalValue = totalValue,
                QualityIssues = qualityIssues
            };
        }

        public async Task<IEnumerable<GoodsReceiptListDto>> GetRecentGoodsReceiptsAsync(int count = 10)
        {
            return await _context.GoodsReceipts
                .Include(gr => gr.PurchaseOrder)
                .Include(gr => gr.Vendor)
                .Include(gr => gr.Items)
                .Where(gr => gr.IsActive)
                .OrderByDescending(gr => gr.CreatedDate)
                .Take(count)
                .Select(gr => new GoodsReceiptListDto
                {
                    Id = gr.Id,
                    ReceiptNumber = gr.ReceiptNumber,
                    PurchaseOrderNumber = gr.PurchaseOrder.OrderNumber,
                    VendorName = gr.Vendor.Name,
                    ReceiptDate = gr.ReceiptDate,
                    Status = gr.Status.ToString(),
                    VendorInvoiceNumber = gr.VendorInvoiceNumber,
                    ReceivedBy = gr.ReceivedBy,
                    TotalAmount = gr.TotalAmount,
                    CreatedDate = gr.CreatedDate,
                    ItemCount = gr.Items.Count
                })
                .ToListAsync();
        }
    }
}
