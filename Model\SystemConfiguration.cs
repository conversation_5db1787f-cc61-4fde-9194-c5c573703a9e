using System.ComponentModel.DataAnnotations;

namespace JayERP.Model
{
    public enum ConfigurationType
    {
        System = 1,
        Business = 2,
        Security = 3,
        Notification = 4,
        Integration = 5
    }

    public class SystemConfiguration : BaseEntity
    {
        [Required]
        [MaxLength(100)]
        public string ConfigKey { get; set; } = string.Empty;
        
        [Required]
        [MaxLength(1000)]
        public string ConfigValue { get; set; } = string.Empty;
        
        [Required]
        [MaxLength(200)]
        public string DisplayName { get; set; } = string.Empty;
        
        [MaxLength(1000)]
        public string Description { get; set; } = string.Empty;
        
        public ConfigurationType ConfigurationType { get; set; }
        
        [MaxLength(50)]
        public string DataType { get; set; } = "String"; // String, Integer, Decimal, Boolean, Date
        
        [MaxLength(1000)]
        public string DefaultValue { get; set; } = string.Empty;
        
        [MaxLength(1000)]
        public string ValidValues { get; set; } = string.Empty; // Comma-separated for dropdown
        
        public bool IsRequired { get; set; } = false;
        
        public bool IsReadOnly { get; set; } = false;
        
        public bool IsSystemConfig { get; set; } = false;
        
        [Range(1, int.MaxValue)]
        public int DisplayOrder { get; set; } = 1;
        
        [MaxLength(100)]
        public string Category { get; set; } = string.Empty;
        
        [MaxLength(100)]
        public string SubCategory { get; set; } = string.Empty;
    }
}
