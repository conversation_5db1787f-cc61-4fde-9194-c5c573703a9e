@using JayERP.DTOs
@using JayERP.Services
@using JayERP.Model
@using Syncfusion.Blazor.Buttons
@using Syncfusion.Blazor.Inputs
@using Syncfusion.Blazor.DropDowns
@using Syncfusion.Blazor.Calendars
@using Syncfusion.Blazor.Grids

@inject IPurchaseOrderService PurchaseOrderService
@inject IVendorService VendorService
@inject IJSRuntime JSRuntime

<div class="container-fluid">
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Purchase Order Information</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Order Number <span class="text-danger">*</span></label>
                                <SfTextBox @bind-Value="purchaseOrderModel.OrderNumber"
                                          Placeholder="Auto-generated"
                                          Readonly="true"
                                          CssClass="@GetValidationClass("OrderNumber")">
                                </SfTextBox>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Vendor <span class="text-danger">*</span></label>
                                <SfDropDownList TValue="int" TItem="VendorListDto"
                                               @bind-Value="purchaseOrderModel.VendorId"
                                               DataSource="vendors"
                                               Placeholder="Select Vendor"
                                               CssClass="@GetValidationClass("VendorId")">
                                    <DropDownListFieldSettings Text="Name" Value="Id"></DropDownListFieldSettings>
                                </SfDropDownList>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Order Date <span class="text-danger">*</span></label>
                                <SfDatePicker @bind-Value="purchaseOrderModel.OrderDate"
                                             Format="dd/MM/yyyy"
                                             CssClass="@GetValidationClass("OrderDate")">
                                </SfDatePicker>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Expected Delivery Date</label>
                                <SfDatePicker @bind-Value="purchaseOrderModel.ExpectedDeliveryDate"
                                             Format="dd/MM/yyyy">
                                </SfDatePicker>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Payment Terms <span class="text-danger">*</span></label>
                                <SfDropDownList TValue="PaymentTerms" TItem="PaymentTermsOption"
                                               @bind-Value="purchaseOrderModel.PaymentTerms"
                                               DataSource="paymentTermsOptions"
                                               Placeholder="Select Payment Terms"
                                               CssClass="@GetValidationClass("PaymentTerms")">
                                    <DropDownListFieldSettings Text="Text" Value="Value"></DropDownListFieldSettings>
                                </SfDropDownList>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Status</label>
                                <SfDropDownList TValue="PurchaseOrderStatus" TItem="StatusOption"
                                               @bind-Value="purchaseOrderModel.Status"
                                               DataSource="statusOptions"
                                               Enabled="@(PurchaseOrder != null)">
                                    <DropDownListFieldSettings Text="Text" Value="Value"></DropDownListFieldSettings>
                                </SfDropDownList>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-12">
                            <div class="mb-3">
                                <label class="form-label">Delivery Address <span class="text-danger">*</span></label>
                                <SfTextBox @bind-Value="purchaseOrderModel.DeliveryAddress"
                                          Placeholder="Enter delivery address"
                                          Multiline="true"
                                          CssClass="@GetValidationClass("DeliveryAddress")">
                                </SfTextBox>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-12">
                            <div class="mb-3">
                                <label class="form-label">Notes</label>
                                <SfTextBox @bind-Value="purchaseOrderModel.Notes"
                                          Placeholder="Additional notes..."
                                          Multiline="true">
                                </SfTextBox>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Purchase Order Items -->
            <div class="card mt-3">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">Order Items</h5>
                    <SfButton @onclick="AddNewItem"
                              CssClass="e-small e-primary"
                              IconCss="e-icons e-plus">
                        Add Item
                    </SfButton>
                </div>
                <div class="card-body">
                    @if (purchaseOrderModel.Items.Any())
                    {
                        <SfGrid DataSource="@purchaseOrderModel.Items"
                                AllowPaging="false"
                                Height="300px">
                            <GridColumns>
                                <GridColumn Field="MaterialName" HeaderText="Material" Width="200"></GridColumn>
                                <GridColumn Field="Quantity" HeaderText="Quantity" Width="100" Format="N2" TextAlign="TextAlign.Right"></GridColumn>
                                <GridColumn Field="UnitPrice" HeaderText="Unit Price" Width="120" Format="C2" TextAlign="TextAlign.Right"></GridColumn>
                                <GridColumn HeaderText="Line Total" Width="120" TextAlign="TextAlign.Right">
                                    <Template>
                                        @{
                                            var item = (context as PurchaseOrderItemCreateDto);
                                            var lineTotal = item.Quantity * item.UnitPrice;
                                        }
                                        @lineTotal.ToString("C2")
                                    </Template>
                                </GridColumn>
                                <GridColumn Field="RequiredDate" HeaderText="Required Date" Width="120" Format="d" Type="ColumnType.Date"></GridColumn>
                                <GridColumn HeaderText="Actions" Width="100" TextAlign="TextAlign.Center">
                                    <Template>
                                        @{
                                            var item = (context as PurchaseOrderItemCreateDto);
                                            var index = purchaseOrderModel.Items.IndexOf(item);
                                        }
                                        <SfButton @onclick="() => RemoveItem(index)"
                                                 CssClass="e-small e-danger"
                                                 IconCss="e-icons e-delete">
                                        </SfButton>
                                    </Template>
                                </GridColumn>
                            </GridColumns>
                        </SfGrid>
                    }
                    else
                    {
                        <div class="text-center py-4">
                            <p class="text-muted">No items added yet. Click "Add Item" to get started.</p>
                        </div>
                    }
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <!-- Order Summary -->
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">Order Summary</h6>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between mb-2">
                        <span>Subtotal:</span>
                        <span>@CalculateSubtotal().ToString("C2")</span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span>Tax:</span>
                        <span>@CalculateTax().ToString("C2")</span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span>Discount:</span>
                        <span>@CalculateDiscount().ToString("C2")</span>
                    </div>
                    <hr>
                    <div class="d-flex justify-content-between fw-bold">
                        <span>Total:</span>
                        <span>@CalculateTotal().ToString("C2")</span>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="card mt-3">
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <SfButton @onclick="SavePurchaseOrder"
                                  CssClass="e-primary"
                                  IconCss="e-icons e-save"
                                  Disabled="@isSubmitting">
                            @(isSubmitting ? "Saving..." : "Save Purchase Order")
                        </SfButton>
                        <SfButton @onclick="OnCancelClick"
                                  CssClass="e-outline">
                            Cancel
                        </SfButton>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    [Parameter] public PurchaseOrderDto? PurchaseOrder { get; set; }
    [Parameter] public EventCallback OnSave { get; set; }
    [Parameter] public EventCallback OnCancel { get; set; }

    private PurchaseOrderCreateDto purchaseOrderModel = new();
    private List<VendorListDto> vendors = new();
    private bool isSubmitting = false;

    private List<PaymentTermsOption> paymentTermsOptions = new()
    {
        new PaymentTermsOption { Text = "Net 30", Value = PaymentTerms.Net30 },
        new PaymentTermsOption { Text = "Net 15", Value = PaymentTerms.Net15 },
        new PaymentTermsOption { Text = "Net 7", Value = PaymentTerms.Net7 },
        new PaymentTermsOption { Text = "Advance Payment", Value = PaymentTerms.AdvancePayment },
        new PaymentTermsOption { Text = "Cash on Delivery", Value = PaymentTerms.CashOnDelivery },
        new PaymentTermsOption { Text = "Immediate", Value = PaymentTerms.Immediate }
    };

    private List<StatusOption> statusOptions = new()
    {
        new StatusOption { Text = "Draft", Value = PurchaseOrderStatus.Draft },
        new StatusOption { Text = "Pending Approval", Value = PurchaseOrderStatus.PendingApproval },
        new StatusOption { Text = "Approved", Value = PurchaseOrderStatus.Approved },
        new StatusOption { Text = "Sent", Value = PurchaseOrderStatus.Sent },
        new StatusOption { Text = "Cancelled", Value = PurchaseOrderStatus.Cancelled }
    };

    protected override async Task OnInitializedAsync()
    {
        await LoadVendors();
        if (PurchaseOrder == null)
        {
            purchaseOrderModel.OrderNumber = await PurchaseOrderService.GenerateOrderNumberAsync();
        }
    }

    protected override void OnParametersSet()
    {
        if (PurchaseOrder != null)
        {
            // Map existing purchase order to create model
            purchaseOrderModel = new PurchaseOrderCreateDto
            {
                VendorId = PurchaseOrder.VendorId,
                OrderDate = PurchaseOrder.OrderDate,
                ExpectedDeliveryDate = PurchaseOrder.ExpectedDeliveryDate,
                Status = PurchaseOrder.Status,
                PaymentTerms = PurchaseOrder.PaymentTerms,
                Notes = PurchaseOrder.Notes,
                DeliveryAddress = PurchaseOrder.DeliveryAddress,
                Items = PurchaseOrder.Items.Select(item => new PurchaseOrderItemCreateDto
                {
                    MaterialId = item.MaterialId,
                    Quantity = item.Quantity,
                    UnitPrice = item.UnitPrice,
                    RequiredDate = item.RequiredDate,
                    Notes = item.Notes
                }).ToList()
            };
        }
        else
        {
            purchaseOrderModel = new PurchaseOrderCreateDto();
        }
    }

    private async Task LoadVendors()
    {
        try
        {
            vendors = (await VendorService.GetAllVendorsAsync()).ToList();
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"Error loading vendors: {ex.Message}");
        }
    }

    private void AddNewItem()
    {
        purchaseOrderModel.Items.Add(new PurchaseOrderItemCreateDto
        {
            Quantity = 1,
            UnitPrice = 0
        });
    }

    private void RemoveItem(int index)
    {
        if (index >= 0 && index < purchaseOrderModel.Items.Count)
        {
            purchaseOrderModel.Items.RemoveAt(index);
        }
    }

    private decimal CalculateSubtotal()
    {
        return purchaseOrderModel.Items.Sum(item => item.Quantity * item.UnitPrice);
    }

    private decimal CalculateTax()
    {
        // Simple tax calculation - 10% of subtotal
        return CalculateSubtotal() * 0.1m;
    }

    private decimal CalculateDiscount()
    {
        // No discount for now
        return 0;
    }

    private decimal CalculateTotal()
    {
        return CalculateSubtotal() + CalculateTax() - CalculateDiscount();
    }

    private async Task SavePurchaseOrder()
    {
        if (!ValidateForm()) return;

        isSubmitting = true;
        try
        {
            if (PurchaseOrder != null)
            {
                var updateDto = new PurchaseOrderUpdateDto
                {
                    Id = PurchaseOrder.Id,
                    VendorId = purchaseOrderModel.VendorId,
                    OrderDate = purchaseOrderModel.OrderDate,
                    ExpectedDeliveryDate = purchaseOrderModel.ExpectedDeliveryDate,
                    Status = purchaseOrderModel.Status,
                    PaymentTerms = purchaseOrderModel.PaymentTerms,
                    Notes = purchaseOrderModel.Notes,
                    DeliveryAddress = purchaseOrderModel.DeliveryAddress,
                    Items = purchaseOrderModel.Items
                };

                await PurchaseOrderService.UpdatePurchaseOrderAsync(updateDto, "CurrentUser");
            }
            else
            {
                await PurchaseOrderService.CreatePurchaseOrderAsync(purchaseOrderModel, "CurrentUser");
            }

            await OnSave.InvokeAsync();
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"Error saving purchase order: {ex.Message}");
        }
        finally
        {
            isSubmitting = false;
        }
    }

    private bool ValidateForm()
    {
        if (purchaseOrderModel.VendorId <= 0)
        {
            JSRuntime.InvokeVoidAsync("alert", "Please select a vendor.");
            return false;
        }

        if (string.IsNullOrWhiteSpace(purchaseOrderModel.DeliveryAddress))
        {
            JSRuntime.InvokeVoidAsync("alert", "Please enter a delivery address.");
            return false;
        }

        if (!purchaseOrderModel.Items.Any())
        {
            JSRuntime.InvokeVoidAsync("alert", "Please add at least one item.");
            return false;
        }

        return true;
    }

    private async Task OnCancelClick()
    {
        await OnCancel.InvokeAsync();
    }

    private string GetValidationClass(string fieldName)
    {
        return string.Empty;
    }

    public class PaymentTermsOption
    {
        public string Text { get; set; } = string.Empty;
        public PaymentTerms Value { get; set; }
    }

    public class StatusOption
    {
        public string Text { get; set; } = string.Empty;
        public PurchaseOrderStatus Value { get; set; }
    }
}
