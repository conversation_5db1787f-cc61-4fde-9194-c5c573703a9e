using System.ComponentModel.DataAnnotations;
using JayERP.Model;

namespace JayERP.DTOs
{
    public class GoodsReceiptDto
    {
        public int Id { get; set; }
        public string ReceiptNumber { get; set; } = string.Empty;
        public int PurchaseOrderId { get; set; }
        public string PurchaseOrderNumber { get; set; } = string.Empty;
        public int VendorId { get; set; }
        public string VendorName { get; set; } = string.Empty;
        public DateTime ReceiptDate { get; set; }
        public GoodsReceiptStatus Status { get; set; }
        public string VendorInvoiceNumber { get; set; } = string.Empty;
        public DateTime? VendorInvoiceDate { get; set; }
        public string DeliveryNoteNumber { get; set; } = string.Empty;
        public string ReceivedBy { get; set; } = string.Empty;
        public string InspectedBy { get; set; } = string.Empty;
        public string Notes { get; set; } = string.Empty;
        public decimal TotalAmount { get; set; }
        public List<GoodsReceiptItemDto> Items { get; set; } = new();
        public DateTime CreatedDate { get; set; }
        public string CreatedBy { get; set; } = string.Empty;
    }

    public class GoodsReceiptCreateDto
    {
        [Required(ErrorMessage = "Purchase order is required")]
        public int PurchaseOrderId { get; set; }
        
        [Required(ErrorMessage = "Receipt date is required")]
        public DateTime ReceiptDate { get; set; } = DateTime.Today;
        
        [StringLength(50, ErrorMessage = "Vendor invoice number cannot exceed 50 characters")]
        public string VendorInvoiceNumber { get; set; } = string.Empty;
        
        public DateTime? VendorInvoiceDate { get; set; }
        
        [StringLength(50, ErrorMessage = "Delivery note number cannot exceed 50 characters")]
        public string DeliveryNoteNumber { get; set; } = string.Empty;
        
        [Required(ErrorMessage = "Received by is required")]
        [StringLength(100, ErrorMessage = "Received by cannot exceed 100 characters")]
        public string ReceivedBy { get; set; } = string.Empty;
        
        [StringLength(100, ErrorMessage = "Inspected by cannot exceed 100 characters")]
        public string InspectedBy { get; set; } = string.Empty;
        
        [StringLength(1000, ErrorMessage = "Notes cannot exceed 1000 characters")]
        public string Notes { get; set; } = string.Empty;
        
        [Required(ErrorMessage = "At least one item is required")]
        [MinLength(1, ErrorMessage = "At least one item is required")]
        public List<GoodsReceiptItemCreateDto> Items { get; set; } = new();
    }

    public class GoodsReceiptUpdateDto : GoodsReceiptCreateDto
    {
        public int Id { get; set; }
        public GoodsReceiptStatus Status { get; set; }
    }

    public class GoodsReceiptListDto
    {
        public int Id { get; set; }
        public string ReceiptNumber { get; set; } = string.Empty;
        public string PurchaseOrderNumber { get; set; } = string.Empty;
        public string VendorName { get; set; } = string.Empty;
        public DateTime ReceiptDate { get; set; }
        public string Status { get; set; } = string.Empty;
        public string VendorInvoiceNumber { get; set; } = string.Empty;
        public string ReceivedBy { get; set; } = string.Empty;
        public decimal TotalAmount { get; set; }
        public DateTime CreatedDate { get; set; }
        public int ItemCount { get; set; }
    }

    public class GoodsReceiptItemDto
    {
        public int Id { get; set; }
        public int GoodsReceiptId { get; set; }
        public int PurchaseOrderItemId { get; set; }
        public int MaterialId { get; set; }
        public string MaterialName { get; set; } = string.Empty;
        public string MaterialCode { get; set; } = string.Empty;
        public decimal OrderedQuantity { get; set; }
        public decimal ReceivedQuantity { get; set; }
        public decimal AcceptedQuantity { get; set; }
        public decimal RejectedQuantity { get; set; }
        public decimal UnitPrice { get; set; }
        public decimal LineTotal { get; set; }
        public string BatchNumber { get; set; } = string.Empty;
        public DateTime? ExpiryDate { get; set; }
        public string StorageLocation { get; set; } = string.Empty;
        public string QualityNotes { get; set; } = string.Empty;
        public string RejectionReason { get; set; } = string.Empty;
    }

    public class GoodsReceiptItemCreateDto
    {
        [Required(ErrorMessage = "Purchase order item is required")]
        public int PurchaseOrderItemId { get; set; }
        
        [Required(ErrorMessage = "Received quantity is required")]
        [Range(0, double.MaxValue, ErrorMessage = "Received quantity must be non-negative")]
        public decimal ReceivedQuantity { get; set; }
        
        [Required(ErrorMessage = "Accepted quantity is required")]
        [Range(0, double.MaxValue, ErrorMessage = "Accepted quantity must be non-negative")]
        public decimal AcceptedQuantity { get; set; }
        
        [Range(0, double.MaxValue, ErrorMessage = "Rejected quantity must be non-negative")]
        public decimal RejectedQuantity { get; set; }
        
        [StringLength(50, ErrorMessage = "Batch number cannot exceed 50 characters")]
        public string BatchNumber { get; set; } = string.Empty;
        
        public DateTime? ExpiryDate { get; set; }
        
        [Required(ErrorMessage = "Storage location is required")]
        [StringLength(100, ErrorMessage = "Storage location cannot exceed 100 characters")]
        public string StorageLocation { get; set; } = string.Empty;
        
        [StringLength(500, ErrorMessage = "Quality notes cannot exceed 500 characters")]
        public string QualityNotes { get; set; } = string.Empty;
        
        [StringLength(500, ErrorMessage = "Rejection reason cannot exceed 500 characters")]
        public string RejectionReason { get; set; } = string.Empty;
    }

    public class PendingReceiptDto
    {
        public int PurchaseOrderId { get; set; }
        public string PurchaseOrderNumber { get; set; } = string.Empty;
        public string VendorName { get; set; } = string.Empty;
        public DateTime OrderDate { get; set; }
        public DateTime? ExpectedDeliveryDate { get; set; }
        public decimal TotalAmount { get; set; }
        public int PendingItemsCount { get; set; }
        public List<PendingReceiptItemDto> PendingItems { get; set; } = new();
    }

    public class PendingReceiptItemDto
    {
        public int PurchaseOrderItemId { get; set; }
        public int MaterialId { get; set; }
        public string MaterialName { get; set; } = string.Empty;
        public string MaterialCode { get; set; } = string.Empty;
        public decimal OrderedQuantity { get; set; }
        public decimal ReceivedQuantity { get; set; }
        public decimal PendingQuantity { get; set; }
        public decimal UnitPrice { get; set; }
        public DateTime? RequiredDate { get; set; }
    }
}
