using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace JayERP.Model
{
    public class Product : BaseEntity
    {
        [Required]
        [MaxLength(50)]
        public string Code { get; set; } = string.Empty;
        
        [Required]
        [MaxLength(200)]
        public string Name { get; set; } = string.Empty;
        
        [MaxLength(1000)]
        public string Description { get; set; } = string.Empty;
        
        public ProductCategory Category { get; set; }
        
        public UnitOfMeasure BaseUnit { get; set; }
        
        [Range(0, double.MaxValue)]
        public decimal StandardCost { get; set; }
        
        [Range(0, double.MaxValue)]
        public decimal SellingPrice { get; set; }
        
        [Range(0, double.MaxValue)]
        public decimal WholesalePrice { get; set; }
        
        [Range(0, double.MaxValue)]
        public decimal RetailPrice { get; set; }
        
        [Range(0, int.MaxValue)]
        public int ShelfLifeDays { get; set; }
        
        public bool RequiresBatchTracking { get; set; }
        
        public bool RequiresExpiryTracking { get; set; }
        
        [MaxLength(100)]
        public string PackagingType { get; set; } = string.Empty;
        
        [Range(0, double.MaxValue)]
        public decimal PackagingSize { get; set; }
        
        [MaxLength(50)]
        public string PackagingUnit { get; set; } = string.Empty;
        
        [Range(0, double.MaxValue)]
        public decimal Weight { get; set; }
        
        [MaxLength(200)]
        public string Ingredients { get; set; } = string.Empty;
        
        [MaxLength(500)]
        public string NutritionalInfo { get; set; } = string.Empty;
        
        [MaxLength(500)]
        public string AllergenInfo { get; set; } = string.Empty;
        
        [MaxLength(100)]
        public string Brand { get; set; } = string.Empty;
        
        [MaxLength(50)]
        public string Barcode { get; set; } = string.Empty;
        
        // Navigation Properties
        public virtual ICollection<BOM> BOMs { get; set; } = new List<BOM>();
        public virtual ICollection<ProductionOrder> ProductionOrders { get; set; } = new List<ProductionOrder>();
        public virtual ICollection<SalesOrderItem> SalesOrderItems { get; set; } = new List<SalesOrderItem>();
        public virtual ICollection<FinishedGoodsInventory> FinishedGoodsInventories { get; set; } = new List<FinishedGoodsInventory>();
    }
}
