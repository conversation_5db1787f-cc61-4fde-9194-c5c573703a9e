using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace JayERP.Model
{
    public class JournalEntry : BaseEntity
    {
        [Required]
        [MaxLength(50)]
        public string JournalNumber { get; set; } = string.Empty;
        
        public DateTime TransactionDate { get; set; } = DateTime.UtcNow;
        
        [Required]
        [MaxLength(500)]
        public string Description { get; set; } = string.Empty;
        
        [MaxLength(50)]
        public string ReferenceNumber { get; set; } = string.Empty;
        
        public DocumentType? SourceDocumentType { get; set; }
        
        [MaxLength(50)]
        public string SourceDocumentNumber { get; set; } = string.Empty;
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalDebit { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalCredit { get; set; }
        
        public bool IsPosted { get; set; } = false;
        
        public DateTime? PostedDate { get; set; }
        
        [MaxLength(100)]
        public string PostedBy { get; set; } = string.Empty;
        
        public bool IsReversed { get; set; } = false;
        
        public DateTime? ReversedDate { get; set; }
        
        [MaxLength(100)]
        public string ReversedBy { get; set; } = string.Empty;
        
        [MaxLength(1000)]
        public string Notes { get; set; } = string.Empty;
        
        // Navigation Properties
        public virtual ICollection<JournalEntryLine> JournalEntryLines { get; set; } = new List<JournalEntryLine>();
    }
}
