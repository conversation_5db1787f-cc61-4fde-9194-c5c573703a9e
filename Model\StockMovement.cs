using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace JayERP.Model
{
    public class StockMovement : BaseEntity
    {
        [Required]
        public int MaterialId { get; set; }
        
        [Required]
        [MaxLength(50)]
        public string DocumentNumber { get; set; } = string.Empty;
        
        public DocumentType DocumentType { get; set; }
        
        public StockMovementType MovementType { get; set; }
        
        [Required]
        [MaxLength(100)]
        public string Location { get; set; } = string.Empty;
        
        [MaxLength(50)]
        public string BatchNumber { get; set; } = string.Empty;
        
        public DateTime? ExpiryDate { get; set; }
        
        [Range(0, double.MaxValue)]
        public decimal Quantity { get; set; }
        
        [Range(0, double.MaxValue)]
        public decimal UnitCost { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalCost { get; set; }
        
        public DateTime MovementDate { get; set; } = DateTime.UtcNow;
        
        [MaxLength(500)]
        public string Reference { get; set; } = string.Empty;
        
        [MaxLength(1000)]
        public string Notes { get; set; } = string.Empty;
        
        // Navigation Properties
        [ForeignKey("MaterialId")]
        public virtual Material Material { get; set; } = null!;
    }
}
