using JayERP.Data;
using JayERP.DTOs;
using JayERP.Model;
using Microsoft.EntityFrameworkCore;

namespace JayERP.Services
{
    public class PurchaseOrderService : IPurchaseOrderService
    {
        private readonly ApplicationDbContext _context;

        public PurchaseOrderService(ApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<IEnumerable<PurchaseOrderListDto>> GetAllPurchaseOrdersAsync()
        {
            return await _context.PurchaseOrders
                .Include(po => po.Vendor)
                .Include(po => po.Items)
                .Where(po => po.IsActive)
                .Select(po => new PurchaseOrderListDto
                {
                    Id = po.Id,
                    OrderNumber = po.OrderNumber,
                    VendorName = po.Vendor.Name,
                    OrderDate = po.OrderDate,
                    ExpectedDeliveryDate = po.ExpectedDeliveryDate,
                    Status = po.Status.ToString(),
                    TotalAmount = po.TotalAmount,
                    ApprovedBy = po.ApprovedBy,
                    ApprovedDate = po.ApprovedDate,
                    CreatedDate = po.CreatedDate,
                    ItemCount = po.Items.Count
                })
                .OrderByDescending(po => po.CreatedDate)
                .ToListAsync();
        }

        public async Task<PurchaseOrderDto?> GetPurchaseOrderByIdAsync(int id)
        {
            var purchaseOrder = await _context.PurchaseOrders
                .Include(po => po.Vendor)
                .Include(po => po.Items)
                    .ThenInclude(item => item.Material)
                .FirstOrDefaultAsync(po => po.Id == id && po.IsActive);

            if (purchaseOrder == null) return null;

            return new PurchaseOrderDto
            {
                Id = purchaseOrder.Id,
                OrderNumber = purchaseOrder.OrderNumber,
                VendorId = purchaseOrder.VendorId,
                VendorName = purchaseOrder.Vendor.Name,
                OrderDate = purchaseOrder.OrderDate,
                ExpectedDeliveryDate = purchaseOrder.ExpectedDeliveryDate,
                Status = purchaseOrder.Status,
                PaymentTerms = purchaseOrder.PaymentTerms,
                SubTotal = purchaseOrder.SubTotal,
                TaxAmount = purchaseOrder.TaxAmount,
                DiscountAmount = purchaseOrder.DiscountAmount,
                TotalAmount = purchaseOrder.TotalAmount,
                ApprovedBy = purchaseOrder.ApprovedBy,
                ApprovedDate = purchaseOrder.ApprovedDate,
                Notes = purchaseOrder.Notes,
                DeliveryAddress = purchaseOrder.DeliveryAddress,
                CreatedDate = purchaseOrder.CreatedDate,
                CreatedBy = purchaseOrder.CreatedBy,
                Items = purchaseOrder.Items.Select(item => new PurchaseOrderItemDto
                {
                    Id = item.Id,
                    PurchaseOrderId = item.PurchaseOrderId,
                    MaterialId = item.MaterialId,
                    MaterialName = item.Material.Name,
                    MaterialCode = item.Material.Code,
                    Quantity = item.Quantity,
                    UnitPrice = item.UnitPrice,
                    LineTotal = item.LineTotal,
                    ReceivedQuantity = item.ReceivedQuantity,
                    PendingQuantity = item.PendingQuantity,
                    RequiredDate = item.RequiredDate,
                    Notes = item.Notes
                }).ToList()
            };
        }

        public async Task<PurchaseOrderDto> CreatePurchaseOrderAsync(PurchaseOrderCreateDto createDto, string createdBy)
        {
            var orderNumber = await GenerateOrderNumberAsync();

            var purchaseOrder = new PurchaseOrder
            {
                OrderNumber = orderNumber,
                VendorId = createDto.VendorId,
                OrderDate = createDto.OrderDate,
                ExpectedDeliveryDate = createDto.ExpectedDeliveryDate,
                Status = PurchaseOrderStatus.Draft,
                PaymentTerms = createDto.PaymentTerms,
                Notes = createDto.Notes,
                DeliveryAddress = createDto.DeliveryAddress,
                CreatedBy = createdBy,
                CreatedDate = DateTime.UtcNow,
                IsActive = true
            };

            // Add items
            foreach (var itemDto in createDto.Items)
            {
                var lineTotal = itemDto.Quantity * itemDto.UnitPrice;
                var item = new PurchaseOrderItem
                {
                    MaterialId = itemDto.MaterialId,
                    Quantity = itemDto.Quantity,
                    UnitPrice = itemDto.UnitPrice,
                    LineTotal = lineTotal,
                    PendingQuantity = itemDto.Quantity,
                    RequiredDate = itemDto.RequiredDate,
                    Notes = itemDto.Notes,
                    CreatedBy = createdBy,
                    CreatedDate = DateTime.UtcNow,
                    IsActive = true
                };
                purchaseOrder.Items.Add(item);
            }

            // Calculate totals
            purchaseOrder.SubTotal = purchaseOrder.Items.Sum(i => i.LineTotal);
            purchaseOrder.TotalAmount = purchaseOrder.SubTotal + purchaseOrder.TaxAmount - purchaseOrder.DiscountAmount;

            _context.PurchaseOrders.Add(purchaseOrder);
            await _context.SaveChangesAsync();

            return await GetPurchaseOrderByIdAsync(purchaseOrder.Id) ?? 
                throw new InvalidOperationException("Failed to retrieve created purchase order");
        }

        public async Task<PurchaseOrderDto> UpdatePurchaseOrderAsync(PurchaseOrderUpdateDto updateDto, string modifiedBy)
        {
            var purchaseOrder = await _context.PurchaseOrders
                .Include(po => po.Items)
                .FirstOrDefaultAsync(po => po.Id == updateDto.Id && po.IsActive);

            if (purchaseOrder == null)
                throw new ArgumentException("Purchase order not found");

            if (!await CanEditPurchaseOrderAsync(updateDto.Id))
                throw new InvalidOperationException("Purchase order cannot be edited in current status");

            // Update header
            purchaseOrder.VendorId = updateDto.VendorId;
            purchaseOrder.OrderDate = updateDto.OrderDate;
            purchaseOrder.ExpectedDeliveryDate = updateDto.ExpectedDeliveryDate;
            purchaseOrder.PaymentTerms = updateDto.PaymentTerms;
            purchaseOrder.Notes = updateDto.Notes;
            purchaseOrder.DeliveryAddress = updateDto.DeliveryAddress;
            purchaseOrder.ModifiedBy = modifiedBy;
            purchaseOrder.ModifiedDate = DateTime.UtcNow;

            // Remove existing items
            _context.PurchaseOrderItems.RemoveRange(purchaseOrder.Items);

            // Add updated items
            foreach (var itemDto in updateDto.Items)
            {
                var lineTotal = itemDto.Quantity * itemDto.UnitPrice;
                var item = new PurchaseOrderItem
                {
                    PurchaseOrderId = purchaseOrder.Id,
                    MaterialId = itemDto.MaterialId,
                    Quantity = itemDto.Quantity,
                    UnitPrice = itemDto.UnitPrice,
                    LineTotal = lineTotal,
                    PendingQuantity = itemDto.Quantity,
                    RequiredDate = itemDto.RequiredDate,
                    Notes = itemDto.Notes,
                    CreatedBy = modifiedBy,
                    CreatedDate = DateTime.UtcNow,
                    IsActive = true
                };
                purchaseOrder.Items.Add(item);
            }

            // Recalculate totals
            purchaseOrder.SubTotal = purchaseOrder.Items.Sum(i => i.LineTotal);
            purchaseOrder.TotalAmount = purchaseOrder.SubTotal + purchaseOrder.TaxAmount - purchaseOrder.DiscountAmount;

            await _context.SaveChangesAsync();

            return await GetPurchaseOrderByIdAsync(purchaseOrder.Id) ?? 
                throw new InvalidOperationException("Failed to retrieve updated purchase order");
        }

        public async Task<bool> DeletePurchaseOrderAsync(int id)
        {
            var purchaseOrder = await _context.PurchaseOrders.FindAsync(id);
            if (purchaseOrder == null) return false;

            if (!await CanDeletePurchaseOrderAsync(id))
                return false;

            // Soft delete
            purchaseOrder.IsActive = false;
            purchaseOrder.ModifiedDate = DateTime.UtcNow;

            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> ApprovePurchaseOrderAsync(int id, string approvedBy, string comments = "")
        {
            var purchaseOrder = await _context.PurchaseOrders.FindAsync(id);
            if (purchaseOrder == null || purchaseOrder.Status != PurchaseOrderStatus.PendingApproval)
                return false;

            purchaseOrder.Status = PurchaseOrderStatus.Approved;
            purchaseOrder.ApprovedBy = approvedBy;
            purchaseOrder.ApprovedDate = DateTime.UtcNow;
            purchaseOrder.ModifiedBy = approvedBy;
            purchaseOrder.ModifiedDate = DateTime.UtcNow;

            if (!string.IsNullOrEmpty(comments))
                purchaseOrder.Notes += $"\nApproval Comments: {comments}";

            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> RejectPurchaseOrderAsync(int id, string rejectedBy, string reason)
        {
            var purchaseOrder = await _context.PurchaseOrders.FindAsync(id);
            if (purchaseOrder == null || purchaseOrder.Status != PurchaseOrderStatus.PendingApproval)
                return false;

            purchaseOrder.Status = PurchaseOrderStatus.Draft;
            purchaseOrder.ModifiedBy = rejectedBy;
            purchaseOrder.ModifiedDate = DateTime.UtcNow;
            purchaseOrder.Notes += $"\nRejection Reason: {reason}";

            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<IEnumerable<PurchaseOrderListDto>> GetPendingApprovalsAsync()
        {
            return await _context.PurchaseOrders
                .Include(po => po.Vendor)
                .Include(po => po.Items)
                .Where(po => po.IsActive && po.Status == PurchaseOrderStatus.PendingApproval)
                .Select(po => new PurchaseOrderListDto
                {
                    Id = po.Id,
                    OrderNumber = po.OrderNumber,
                    VendorName = po.Vendor.Name,
                    OrderDate = po.OrderDate,
                    ExpectedDeliveryDate = po.ExpectedDeliveryDate,
                    Status = po.Status.ToString(),
                    TotalAmount = po.TotalAmount,
                    CreatedDate = po.CreatedDate,
                    ItemCount = po.Items.Count
                })
                .OrderBy(po => po.OrderDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<PurchaseOrderListDto>> GetPurchaseOrdersByVendorAsync(int vendorId)
        {
            return await _context.PurchaseOrders
                .Include(po => po.Vendor)
                .Include(po => po.Items)
                .Where(po => po.IsActive && po.VendorId == vendorId)
                .Select(po => new PurchaseOrderListDto
                {
                    Id = po.Id,
                    OrderNumber = po.OrderNumber,
                    VendorName = po.Vendor.Name,
                    OrderDate = po.OrderDate,
                    ExpectedDeliveryDate = po.ExpectedDeliveryDate,
                    Status = po.Status.ToString(),
                    TotalAmount = po.TotalAmount,
                    ApprovedBy = po.ApprovedBy,
                    ApprovedDate = po.ApprovedDate,
                    CreatedDate = po.CreatedDate,
                    ItemCount = po.Items.Count
                })
                .OrderByDescending(po => po.CreatedDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<PurchaseOrderListDto>> GetPurchaseOrdersByStatusAsync(PurchaseOrderStatus status)
        {
            return await _context.PurchaseOrders
                .Include(po => po.Vendor)
                .Include(po => po.Items)
                .Where(po => po.IsActive && po.Status == status)
                .Select(po => new PurchaseOrderListDto
                {
                    Id = po.Id,
                    OrderNumber = po.OrderNumber,
                    VendorName = po.Vendor.Name,
                    OrderDate = po.OrderDate,
                    ExpectedDeliveryDate = po.ExpectedDeliveryDate,
                    Status = po.Status.ToString(),
                    TotalAmount = po.TotalAmount,
                    ApprovedBy = po.ApprovedBy,
                    ApprovedDate = po.ApprovedDate,
                    CreatedDate = po.CreatedDate,
                    ItemCount = po.Items.Count
                })
                .OrderByDescending(po => po.CreatedDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<PurchaseOrderListDto>> SearchPurchaseOrdersAsync(string searchTerm)
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
                return await GetAllPurchaseOrdersAsync();

            var lowerSearchTerm = searchTerm.ToLower();

            return await _context.PurchaseOrders
                .Include(po => po.Vendor)
                .Include(po => po.Items)
                .Where(po => po.IsActive && 
                    (po.OrderNumber.ToLower().Contains(lowerSearchTerm) ||
                     po.Vendor.Name.ToLower().Contains(lowerSearchTerm) ||
                     po.Notes.ToLower().Contains(lowerSearchTerm)))
                .Select(po => new PurchaseOrderListDto
                {
                    Id = po.Id,
                    OrderNumber = po.OrderNumber,
                    VendorName = po.Vendor.Name,
                    OrderDate = po.OrderDate,
                    ExpectedDeliveryDate = po.ExpectedDeliveryDate,
                    Status = po.Status.ToString(),
                    TotalAmount = po.TotalAmount,
                    ApprovedBy = po.ApprovedBy,
                    ApprovedDate = po.ApprovedDate,
                    CreatedDate = po.CreatedDate,
                    ItemCount = po.Items.Count
                })
                .OrderByDescending(po => po.CreatedDate)
                .ToListAsync();
        }

        public async Task<string> GenerateOrderNumberAsync()
        {
            var year = DateTime.Now.Year;
            var lastOrder = await _context.PurchaseOrders
                .Where(po => po.OrderNumber.StartsWith($"PO-{year}-"))
                .OrderByDescending(po => po.OrderNumber)
                .FirstOrDefaultAsync();

            int nextNumber = 1;
            if (lastOrder != null)
            {
                var lastNumberPart = lastOrder.OrderNumber.Split('-').LastOrDefault();
                if (int.TryParse(lastNumberPart, out int lastNumber))
                {
                    nextNumber = lastNumber + 1;
                }
            }

            return $"PO-{year}-{nextNumber:D4}";
        }

        public async Task<bool> CanDeletePurchaseOrderAsync(int id)
        {
            var purchaseOrder = await _context.PurchaseOrders.FindAsync(id);
            if (purchaseOrder == null) return false;

            // Can only delete draft orders
            return purchaseOrder.Status == PurchaseOrderStatus.Draft;
        }

        public async Task<bool> CanEditPurchaseOrderAsync(int id)
        {
            var purchaseOrder = await _context.PurchaseOrders.FindAsync(id);
            if (purchaseOrder == null) return false;

            // Can only edit draft or pending approval orders
            return purchaseOrder.Status == PurchaseOrderStatus.Draft || 
                   purchaseOrder.Status == PurchaseOrderStatus.PendingApproval;
        }
    }
}
