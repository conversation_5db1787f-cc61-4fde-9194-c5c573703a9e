using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace JayERP.Model
{
    public class LeaveApplication : BaseEntity
    {
        [Required]
        public int EmployeeId { get; set; }
        
        [Required]
        [MaxLength(50)]
        public string ApplicationNumber { get; set; } = string.Empty;
        
        public LeaveType LeaveType { get; set; }
        
        public DateTime FromDate { get; set; }
        
        public DateTime ToDate { get; set; }
        
        [Range(0, int.MaxValue)]
        public int NumberOfDays { get; set; }
        
        [Required]
        [MaxLength(1000)]
        public string Reason { get; set; } = string.Empty;
        
        public ApprovalStatus Status { get; set; } = ApprovalStatus.Pending;
        
        public DateTime ApplicationDate { get; set; } = DateTime.UtcNow;
        
        [MaxLength(100)]
        public string ApprovedBy { get; set; } = string.Empty;
        
        public DateTime? ApprovedDate { get; set; }
        
        [MaxLength(1000)]
        public string ApprovalComments { get; set; } = string.Empty;
        
        [MaxLength(1000)]
        public string RejectionReason { get; set; } = string.Empty;
        
        public bool IsHalfDay { get; set; } = false;
        
        [MaxLength(20)]
        public string HalfDaySession { get; set; } = string.Empty; // Morning/Afternoon
        
        [MaxLength(100)]
        public string ContactDuringLeave { get; set; } = string.Empty;
        
        [MaxLength(500)]
        public string Notes { get; set; } = string.Empty;
        
        // Navigation Properties
        [ForeignKey("EmployeeId")]
        public virtual Employee Employee { get; set; } = null!;
    }
}
