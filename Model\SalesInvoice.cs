using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace JayERP.Model
{
    public class SalesInvoice : BaseEntity
    {
        [Required]
        [MaxLength(50)]
        public string InvoiceNumber { get; set; } = string.Empty;
        
        [Required]
        public int CustomerId { get; set; }
        
        public int? SalesOrderId { get; set; }
        
        public int? DeliveryId { get; set; }
        
        public DateTime InvoiceDate { get; set; } = DateTime.UtcNow;
        
        public DateTime DueDate { get; set; }
        
        public InvoiceStatus Status { get; set; } = InvoiceStatus.Draft;
        
        public PaymentTerms PaymentTerms { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal SubTotal { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal TaxAmount { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal DiscountAmount { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal ShippingCharges { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalAmount { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal PaidAmount { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal BalanceAmount { get; set; }
        
        [MaxLength(50)]
        public string CustomerPO { get; set; } = string.Empty;
        
        [MaxLength(100)]
        public string SalesRepresentative { get; set; } = string.Empty;
        
        [MaxLength(1000)]
        public string Notes { get; set; } = string.Empty;
        
        // Navigation Properties
        [ForeignKey("CustomerId")]
        public virtual Customer Customer { get; set; } = null!;
        
        [ForeignKey("SalesOrderId")]
        public virtual SalesOrder? SalesOrder { get; set; }
        
        [ForeignKey("DeliveryId")]
        public virtual Delivery? Delivery { get; set; }
        
        public virtual ICollection<SalesInvoiceItem> Items { get; set; } = new List<SalesInvoiceItem>();
        public virtual ICollection<PaymentTransaction> PaymentTransactions { get; set; } = new List<PaymentTransaction>();
    }
}
