@page "/purchase-orders"
@using JayERP.DTOs
@using JayERP.Services
@using JayERP.Model
@using Syncfusion.Blazor.Grids
@using Syncfusion.Blazor.Buttons
@using Syncfusion.Blazor.Inputs
@using Syncfusion.Blazor.DropDowns
@using Syncfusion.Blazor.Popups

@inject IPurchaseOrderService PurchaseOrderService
@inject IVendorService VendorService
@inject IJSRuntime JSRuntime

<PageTitle>Purchase Orders</PageTitle>

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h3 class="mb-0">Purchase Orders</h3>
        <SfButton @onclick="ShowAddPurchaseOrderDialog"
                  CssClass="e-primary"
                  IconCss="e-icons e-plus">
            Add Purchase Order
        </SfButton>
    </div>

    <!-- Filters Section -->
    <div class="card mb-4">
        <div class="card-body">
            <div class="row g-3">
                <div class="col-md-4">
                    <label class="form-label">Search</label>
                    <SfTextBox @bind-Value="searchText"
                              Placeholder="Search by order number, vendor..."
                              ShowClearButton="true"
                              @onchange="OnSearchChanged">
                    </SfTextBox>
                </div>
                <div class="col-md-3">
                    <label class="form-label">Status</label>
                    <SfDropDownList TValue="PurchaseOrderStatus?" TItem="StatusOption"
                                   @bind-Value="selectedStatus"
                                   DataSource="statusOptions"
                                   Placeholder="All Statuses"
                                   AllowClear="true">
                        <DropDownListFieldSettings Text="Text" Value="Value"></DropDownListFieldSettings>
                        <DropDownListEvents TValue="PurchaseOrderStatus?" TItem="StatusOption" ValueChange="OnStatusChanged"></DropDownListEvents>
                    </SfDropDownList>
                </div>
                <div class="col-md-3">
                    <label class="form-label">Date Range</label>
                    <SfTextBox Value="All Dates" Readonly="true"></SfTextBox>
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <SfButton @onclick="ClearFilters"
                              CssClass="e-outline"
                              IconCss="e-icons e-clear-all">
                        Clear
                    </SfButton>
                </div>
            </div>
        </div>
    </div>

    <div class="grid-container">
        <SfGrid DataSource="@purchaseOrders"
                AllowPaging="true"
                AllowSorting="true"
                AllowFiltering="false"
                Height="calc(100vh - 330px)">
            <GridPageSettings PageSize="20"></GridPageSettings>
            <GridColumns>
                <GridColumn Field="@nameof(PurchaseOrderListDto.OrderNumber)" HeaderText="Order Number" Width="120"></GridColumn>
                <GridColumn Field="@nameof(PurchaseOrderListDto.VendorName)" HeaderText="Vendor" Width="150"></GridColumn>
                <GridColumn Field="@nameof(PurchaseOrderListDto.OrderDate)" HeaderText="Order Date" Width="120" Format="d" Type="ColumnType.Date"></GridColumn>
                <GridColumn Field="@nameof(PurchaseOrderListDto.ExpectedDeliveryDate)" HeaderText="Expected Delivery" Width="130" Format="d" Type="ColumnType.Date"></GridColumn>
                <GridColumn Field="@nameof(PurchaseOrderListDto.TotalAmount)" HeaderText="Total Amount" Width="120" Format="C2" TextAlign="TextAlign.Right"></GridColumn>
                <GridColumn Field="@nameof(PurchaseOrderListDto.ItemCount)" HeaderText="Items" Width="80" TextAlign="TextAlign.Center"></GridColumn>
                <GridColumn Field="@nameof(PurchaseOrderListDto.Status)" HeaderText="Status" Width="120" TextAlign="TextAlign.Center">
                    <Template>
                        @{
                            var orderItem = (context as PurchaseOrderListDto);
                            var badgeClass = GetStatusBadgeClass(orderItem.Status);
                        }
                        <span class="badge @badgeClass">
                            @orderItem.Status
                        </span>
                    </Template>
                </GridColumn>
                <GridColumn Field="@nameof(PurchaseOrderListDto.ApprovedBy)" HeaderText="Approved By" Width="120"></GridColumn>
                <GridColumn HeaderText="Actions" Width="150" TextAlign="TextAlign.Center">
                    <Template>
                        @{
                            var orderItem = (context as PurchaseOrderListDto);
                        }
                        <div class="btn-group" role="group">
                            <SfButton @onclick="() => ViewPurchaseOrder(orderItem.Id)"
                                     CssClass="e-small e-info"
                                     IconCss="e-icons e-eye">
                            </SfButton>
                            <SfButton @onclick="() => EditPurchaseOrder(orderItem.Id)"
                                     CssClass="e-small e-warning"
                                     IconCss="e-icons e-edit">
                            </SfButton>
                            <SfButton @onclick="() => DeletePurchaseOrder(orderItem.Id)"
                                     CssClass="e-small e-danger"
                                     IconCss="e-icons e-delete">
                            </SfButton>
                        </div>
                    </Template>
                </GridColumn>
            </GridColumns>
        </SfGrid>
    </div>
</div>

<!-- Purchase Order Form Dialog -->
<SfDialog @bind-Visible="showPurchaseOrderDialog"
          Header="@dialogTitle"
          Width="90%"
          Height="80%"
          IsModal="true"
          ShowCloseIcon="true"
          AllowDragging="true">
    <DialogEvents OnOpen="OnDialogOpen"></DialogEvents>
    <ContentTemplate>
        <PurchaseOrderForm @ref="purchaseOrderForm"
                          PurchaseOrder="currentPurchaseOrder"
                          OnSave="OnPurchaseOrderSaved"
                          OnCancel="OnPurchaseOrderCancelled" />
    </ContentTemplate>
</SfDialog>

<!-- Purchase Order Details Dialog -->
<SfDialog @bind-Visible="showPurchaseOrderDetailsDialog"
          Header="Purchase Order Details"
          Width="90%"
          Height="80%"
          IsModal="true"
          ShowCloseIcon="true"
          AllowDragging="true">
    <ContentTemplate>
        <PurchaseOrderDetails @ref="purchaseOrderDetails"
                             PurchaseOrderId="selectedPurchaseOrderId" />
    </ContentTemplate>
</SfDialog>

@code {
    private PurchaseOrderForm? purchaseOrderForm;
    private PurchaseOrderDetails? purchaseOrderDetails;

    private List<PurchaseOrderListDto> purchaseOrders = new();
    private List<PurchaseOrderListDto> allPurchaseOrders = new();
    private string searchText = string.Empty;
    private PurchaseOrderStatus? selectedStatus;
    private bool showPurchaseOrderDialog = false;
    private bool showPurchaseOrderDetailsDialog = false;
    private string dialogTitle = string.Empty;
    private PurchaseOrderDto? currentPurchaseOrder;
    private int selectedPurchaseOrderId;

    private List<StatusOption> statusOptions = new()
    {
        new StatusOption { Text = "All Statuses", Value = null },
        new StatusOption { Text = "Draft", Value = PurchaseOrderStatus.Draft },
        new StatusOption { Text = "Pending Approval", Value = PurchaseOrderStatus.PendingApproval },
        new StatusOption { Text = "Approved", Value = PurchaseOrderStatus.Approved },
        new StatusOption { Text = "Sent", Value = PurchaseOrderStatus.Sent },
        new StatusOption { Text = "Partially Received", Value = PurchaseOrderStatus.PartiallyReceived },
        new StatusOption { Text = "Fully Received", Value = PurchaseOrderStatus.FullyReceived },
        new StatusOption { Text = "Cancelled", Value = PurchaseOrderStatus.Cancelled },
        new StatusOption { Text = "Closed", Value = PurchaseOrderStatus.Closed }
    };

    protected override async Task OnInitializedAsync()
    {
        await LoadPurchaseOrders();
    }

    private async Task LoadPurchaseOrders()
    {
        try
        {
            allPurchaseOrders = (await PurchaseOrderService.GetAllPurchaseOrdersAsync()).ToList();
            ApplyFilters();
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"Error loading purchase orders: {ex.Message}");
        }
    }

    private void ApplyFilters()
    {
        var filtered = allPurchaseOrders.AsEnumerable();

        if (!string.IsNullOrWhiteSpace(searchText))
        {
            filtered = filtered.Where(po => 
                po.OrderNumber.Contains(searchText, StringComparison.OrdinalIgnoreCase) ||
                po.VendorName.Contains(searchText, StringComparison.OrdinalIgnoreCase));
        }

        if (selectedStatus.HasValue)
        {
            filtered = filtered.Where(po => po.Status == selectedStatus.Value.ToString());
        }

        purchaseOrders = filtered.ToList();
    }

    private void OnSearchChanged()
    {
        ApplyFilters();
    }

    private void OnStatusChanged(ChangeEventArgs<PurchaseOrderStatus?, StatusOption> args)
    {
        selectedStatus = args.Value;
        ApplyFilters();
    }

    private void ClearFilters()
    {
        searchText = string.Empty;
        selectedStatus = null;
        ApplyFilters();
    }

    private void ShowAddPurchaseOrderDialog()
    {
        dialogTitle = "Add New Purchase Order";
        currentPurchaseOrder = null;
        showPurchaseOrderDialog = true;
    }

    private async Task EditPurchaseOrder(int purchaseOrderId)
    {
        try
        {
            currentPurchaseOrder = await PurchaseOrderService.GetPurchaseOrderByIdAsync(purchaseOrderId);
            if (currentPurchaseOrder != null)
            {
                dialogTitle = "Edit Purchase Order";
                showPurchaseOrderDialog = true;
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"Error loading purchase order: {ex.Message}");
        }
    }

    private async Task ViewPurchaseOrder(int purchaseOrderId)
    {
        selectedPurchaseOrderId = purchaseOrderId;
        showPurchaseOrderDetailsDialog = true;
    }

    private async Task DeletePurchaseOrder(int purchaseOrderId)
    {
        var confirmed = await JSRuntime.InvokeAsync<bool>("confirm", "Are you sure you want to delete this purchase order?");
        if (confirmed)
        {
            try
            {
                await PurchaseOrderService.DeletePurchaseOrderAsync(purchaseOrderId);
                await LoadPurchaseOrders();
                await JSRuntime.InvokeVoidAsync("alert", "Purchase order deleted successfully!");
            }
            catch (Exception ex)
            {
                await JSRuntime.InvokeVoidAsync("alert", $"Error deleting purchase order: {ex.Message}");
            }
        }
    }

    private async Task OnPurchaseOrderSaved()
    {
        showPurchaseOrderDialog = false;
        await LoadPurchaseOrders();
    }

    private void OnPurchaseOrderCancelled()
    {
        showPurchaseOrderDialog = false;
    }

    private void OnDialogOpen()
    {
        // Dialog opened
    }

    private string GetStatusBadgeClass(string status)
    {
        return status switch
        {
            "Draft" => "bg-secondary",
            "PendingApproval" => "bg-warning",
            "Approved" => "bg-success",
            "Sent" => "bg-info",
            "PartiallyReceived" => "bg-primary",
            "FullyReceived" => "bg-success",
            "Cancelled" => "bg-danger",
            "Closed" => "bg-dark",
            _ => "bg-secondary"
        };
    }

    public class StatusOption
    {
        public string Text { get; set; } = string.Empty;
        public PurchaseOrderStatus? Value { get; set; }
    }
}
