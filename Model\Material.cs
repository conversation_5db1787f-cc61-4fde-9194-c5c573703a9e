using System.ComponentModel.DataAnnotations;

namespace JayERP.Model
{
    public class Material : BaseEntity
    {
        [Required]
        [MaxLength(50)]
        public string Code { get; set; } = string.Empty;
        
        [Required]
        [MaxLength(200)]
        public string Name { get; set; } = string.Empty;
        
        [MaxLength(1000)]
        public string Description { get; set; } = string.Empty;
        
        public MaterialCategory Category { get; set; }
        
        public UnitOfMeasure BaseUnit { get; set; }
        
        [Range(0, double.MaxValue)]
        public decimal StandardCost { get; set; }
        
        [Range(0, double.MaxValue)]
        public decimal CurrentCost { get; set; }
        
        [Range(0, int.MaxValue)]
        public int ReorderLevel { get; set; }
        
        [Range(0, int.MaxValue)]
        public int MaxStockLevel { get; set; }
        
        [Range(0, int.MaxValue)]
        public int MinStockLevel { get; set; }
        
        public int? ShelfLifeDays { get; set; }
        
        public bool IsPerishable { get; set; }
        
        public bool RequiresBatchTracking { get; set; }
        
        public bool RequiresExpiryTracking { get; set; }
        
        [MaxLength(100)]
        public string StorageLocation { get; set; } = string.Empty;
        
        [MaxLength(500)]
        public string StorageConditions { get; set; } = string.Empty;
        
        [MaxLength(100)]
        public string Supplier { get; set; } = string.Empty;
        
        [MaxLength(50)]
        public string SupplierCode { get; set; } = string.Empty;
        
        // Navigation Properties
        public virtual ICollection<StockMovement> StockMovements { get; set; } = new List<StockMovement>();
        public virtual ICollection<BOMItem> BOMItems { get; set; } = new List<BOMItem>();
        public virtual ICollection<PurchaseOrderItem> PurchaseOrderItems { get; set; } = new List<PurchaseOrderItem>();
        public virtual ICollection<InventoryBalance> InventoryBalances { get; set; } = new List<InventoryBalance>();
    }
}
