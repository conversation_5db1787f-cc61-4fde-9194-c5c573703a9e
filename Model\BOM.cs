using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace JayERP.Model
{
    public class BOM : BaseEntity
    {
        [Required]
        public int ProductId { get; set; }
        
        [Required]
        [MaxLength(50)]
        public string Version { get; set; } = "1.0";
        
        [Range(0, double.MaxValue)]
        public decimal BatchSize { get; set; }
        
        [Range(0, double.MaxValue)]
        public decimal YieldPercentage { get; set; } = 100;
        
        public DateTime EffectiveDate { get; set; } = DateTime.UtcNow;
        
        public DateTime? ExpiryDate { get; set; }
        
        public bool IsDefault { get; set; } = true;
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal MaterialCost { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal LaborCost { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal OverheadCost { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalCost { get; set; }
        
        [Range(0, int.MaxValue)]
        public int EstimatedProductionTimeMinutes { get; set; }
        
        [MaxLength(1000)]
        public string Instructions { get; set; } = string.Empty;
        
        [MaxLength(500)]
        public string QualityParameters { get; set; } = string.Empty;
        
        // Navigation Properties
        [ForeignKey("ProductId")]
        public virtual Product Product { get; set; } = null!;
        
        public virtual ICollection<BOMItem> Items { get; set; } = new List<BOMItem>();
    }
}
