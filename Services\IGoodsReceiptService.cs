using JayERP.DTOs;
using JayERP.Model;

namespace JayERP.Services
{
    public interface IGoodsReceiptService
    {
        Task<IEnumerable<GoodsReceiptListDto>> GetAllGoodsReceiptsAsync();
        Task<GoodsReceiptDto?> GetGoodsReceiptByIdAsync(int id);
        Task<GoodsReceiptDto> CreateGoodsReceiptAsync(GoodsReceiptCreateDto createDto, string createdBy);
        Task<GoodsReceiptDto> UpdateGoodsReceiptAsync(GoodsReceiptUpdateDto updateDto, string modifiedBy);
        Task<bool> DeleteGoodsReceiptAsync(int id);
        Task<bool> CompleteGoodsReceiptAsync(int id, string completedBy);
        Task<bool> RejectGoodsReceiptAsync(int id, string rejectedBy, string reason);
        Task<IEnumerable<GoodsReceiptListDto>> GetGoodsReceiptsByVendorAsync(int vendorId);
        Task<IEnumerable<GoodsReceiptListDto>> GetGoodsReceiptsByPurchaseOrderAsync(int purchaseOrderId);
        Task<IEnumerable<GoodsReceiptListDto>> GetGoodsReceiptsByStatusAsync(GoodsReceiptStatus status);
        Task<IEnumerable<GoodsReceiptListDto>> SearchGoodsReceiptsAsync(string searchTerm);
        Task<IEnumerable<PendingReceiptDto>> GetPendingReceiptsAsync();
        Task<PendingReceiptDto?> GetPendingReceiptByPurchaseOrderAsync(int purchaseOrderId);
        Task<string> GenerateReceiptNumberAsync();
        Task<bool> CanDeleteGoodsReceiptAsync(int id);
        Task<bool> CanEditGoodsReceiptAsync(int id);
        Task<bool> GoodsReceiptExistsAsync(int id);
        Task<GoodsReceiptSummaryDto> GetGoodsReceiptSummaryAsync();
        Task<IEnumerable<GoodsReceiptListDto>> GetRecentGoodsReceiptsAsync(int count = 10);
    }
}
