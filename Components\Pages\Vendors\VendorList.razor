@page "/vendors"
@using Syncfusion.Blazor.Inputs
@using Syncfusion.Blazor.DropDowns
@using Syncfusion.Blazor.Buttons
@using Syncfusion.Blazor.Grids
@inject IVendorService VendorService
@inject IJSRuntime JSRuntime
@rendermode InteractiveServer

<PageTitle>Vendor Management</PageTitle>

<div class="">
    <div class="row">
        <div class="col">
            <h3>Vendor Management</h3>
        </div>
        <div class="col-auto">
            <SfButton @onclick="ShowAddVendorDialog"
                     IsPrimary="true"
                     IconCss="e-icons e-plus">
                Add Vendor
            </SfButton>
        </div>
    </div>

    <div class="row mb-3">
        <div class="col-md-4">
            <SfTextBox @bind-Value="searchText"
                      @oninput="OnSearchTextChanged"
                      Placeholder="Search vendors..."
                      ShowClearButton="true"
                      FloatLabelType="FloatLabelType.Auto">
            </SfTextBox>
        </div>
        <div class="col-md-3">
            <SfDropDownList TValue="VendorCategory?" TItem="CategoryOption"
                           Value="selectedCategory"
                           ValueChanged="@OnCategoryChanged"
                           DataSource="@categoryOptions"
                           Placeholder="All Categories"
                           FloatLabelType="FloatLabelType.Auto">
                <DropDownListFieldSettings Text="Text" Value="Value"></DropDownListFieldSettings>
            </SfDropDownList>
        </div>
        <div class="col-md-2">
            
        </div>
    </div>

    <div class="grid-container">
        <SfGrid DataSource="@vendors"
                AllowPaging="true"
                AllowSorting="true"
                AllowFiltering="false"
                Height="calc(100vh - 330px)">
            <GridPageSettings PageSize="20"></GridPageSettings>
            <GridColumns>
                <GridColumn Field="@nameof(VendorListDto.Name)" HeaderText="Vendor Name" Width="150"></GridColumn>
                <GridColumn Field="@nameof(VendorListDto.CompanyName)" HeaderText="Company" Width="150"></GridColumn>
                <GridColumn Field="@nameof(VendorListDto.Category)" HeaderText="Category" Width="120"></GridColumn>
                <GridColumn Field="@nameof(VendorListDto.Email)" HeaderText="Email" Width="180"></GridColumn>
                <GridColumn Field="@nameof(VendorListDto.Phone)" HeaderText="Phone" Width="120"></GridColumn>
                <GridColumn Field="@nameof(VendorListDto.City)" HeaderText="City" Width="100"></GridColumn>
                <GridColumn Field="@nameof(VendorListDto.PaymentTerms)" HeaderText="Payment Terms" Width="120"></GridColumn>
                <GridColumn Field="@nameof(VendorListDto.CreditLimit)" HeaderText="Credit Limit" Width="120" Format="C2"></GridColumn>
                <GridColumn HeaderText="Quality" Width="100" TextAlign="TextAlign.Center">
                    <Template>
                        @{
                            var vendorItem = (context as VendorListDto);
                        }
                        <div class="rating">
                            @for (int i = 1; i <= 5; i++)
                            {
                                <i class="@(i <= vendorItem.QualityRating ? "fas fa-star text-warning" : "far fa-star text-muted")"></i>
                            }
                        </div>
                    </Template>
                </GridColumn>
                <GridColumn HeaderText="Delivery" Width="100" TextAlign="TextAlign.Center">
                    <Template>
                        @{
                            var vendorItem = (context as VendorListDto);
                        }
                        <div class="rating">
                            @for (int i = 1; i <= 5; i++)
                            {
                                <i class="@(i <= vendorItem.DeliveryRating ? "fas fa-star text-warning" : "far fa-star text-muted")"></i>
                            }
                        </div>
                    </Template>
                </GridColumn>
                <GridColumn HeaderText="Status" Width="100" TextAlign="TextAlign.Center">
                    <Template>
                        @{
                            var vendorItem = (context as VendorListDto);
                        }
                        <span class="badge @(vendorItem.IsActive ? "bg-success" : "bg-secondary")">
                            @(vendorItem.IsActive ? "Active" : "Inactive")
                        </span>
                    </Template>
                </GridColumn>
                <GridColumn HeaderText="Actions" Width="150" TextAlign="TextAlign.Center">
                    <Template>
                        @{
                            var vendorItem = (context as VendorListDto);
                        }
                        <div class="btn-group" role="group">
                            <SfButton @onclick="() => ViewVendor(vendorItem.Id)"
                                     CssClass="e-small e-info"
                                     IconCss="e-icons e-eye">
                            </SfButton>
                            <SfButton @onclick="() => EditVendor(vendorItem.Id)"
                                     CssClass="e-small e-warning"
                                     IconCss="e-icons e-edit">
                            </SfButton>
                            <SfButton @onclick="() => DeleteVendor(vendorItem.Id)"
                                     CssClass="e-small e-danger"
                                     IconCss="e-icons e-delete">
                            </SfButton>
                        </div>
                    </Template>
                </GridColumn>
            </GridColumns>
        </SfGrid>
    </div>
</div>

<!-- Add/Edit Vendor Modal -->
<div class="modal fade @(showVendorDialog ? "show" : "")" style="display: @(showVendorDialog ? "block" : "none")" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">@dialogTitle</h5>
                <button type="button" class="btn-close" @onclick="OnVendorCancelled"></button>
            </div>
            <div class="modal-body">
                <VendorForm @ref="vendorForm" Vendor="@currentVendor"
                           OnSave="@OnVendorSaved" OnCancel="@OnVendorCancelled" />
            </div>
        </div>
    </div>
</div>

<!-- Vendor Details Modal -->
<div class="modal fade @(showVendorDetailsDialog ? "show" : "")" style="display: @(showVendorDetailsDialog ? "block" : "none")" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Vendor Details</h5>
                <button type="button" class="btn-close" @onclick="() => showVendorDetailsDialog = false"></button>
            </div>
            <div class="modal-body">
                <VendorDetails @ref="vendorDetails" VendorId="@selectedVendorId" />
            </div>
        </div>
    </div>
</div>

@if (showVendorDialog || showVendorDetailsDialog)
{
    <div class="modal-backdrop fade show"></div>
}

@code {
    private VendorForm? vendorForm;
    private VendorDetails? vendorDetails;

    private List<VendorListDto> vendors = new();
    private List<VendorListDto> allVendors = new();
    private string searchText = string.Empty;
    private VendorCategory? selectedCategory;
    private bool showVendorDialog = false;
    private bool showVendorDetailsDialog = false;
    private string dialogTitle = string.Empty;
    private VendorDto? currentVendor;
    private int selectedVendorId;

    private List<CategoryOption> categoryOptions = new()
    {
        new CategoryOption { Text = "All Categories", Value = null },
        new CategoryOption { Text = "Ingredients", Value = VendorCategory.Ingredients },
        new CategoryOption { Text = "Packaging", Value = VendorCategory.Packaging },
        new CategoryOption { Text = "Supplies", Value = VendorCategory.Supplies },
        new CategoryOption { Text = "Services", Value = VendorCategory.Services }
    };

    protected override async Task OnInitializedAsync()
    {
        await LoadVendors();
    }

    private async Task LoadVendors()
    {
        try
        {
            allVendors = (await VendorService.GetAllVendorsAsync()).ToList();
            ApplyFilters();
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"Error loading vendors: {ex.Message}");
        }
    }

    private void ApplyFilters()
    {
        var filteredVendors = allVendors.AsQueryable();

        if (!string.IsNullOrWhiteSpace(searchText))
        {
            var lowerSearch = searchText.ToLower();
            filteredVendors = filteredVendors.Where(v =>
                v.Name.ToLower().Contains(lowerSearch) ||
                v.CompanyName.ToLower().Contains(lowerSearch) ||
                v.Email.ToLower().Contains(lowerSearch) ||
                v.City.ToLower().Contains(lowerSearch));
        }

        if (selectedCategory.HasValue)
        {
            filteredVendors = filteredVendors.Where(v => v.Category == selectedCategory.Value.ToString());
        }

        vendors = filteredVendors.ToList();
        StateHasChanged();
    }

    private async Task OnSearchTextChanged(ChangeEventArgs args)
    {
        searchText = args.Value?.ToString() ?? string.Empty;
        ApplyFilters();
    }

    private void OnCategoryChanged(VendorCategory? value)
    {
        selectedCategory = value;
        ApplyFilters();
    }

    private void ClearFilters()
    {
        searchText = string.Empty;
        selectedCategory = null;
        ApplyFilters();
    }

    private void ShowAddVendorDialog()
    {
        dialogTitle = "Add New Vendor";
        currentVendor = null;
        showVendorDialog = true;
    }

    private async Task EditVendor(int vendorId)
    {
        try
        {
            currentVendor = await VendorService.GetVendorByIdAsync(vendorId);
            if (currentVendor != null)
            {
                dialogTitle = "Edit Vendor";
                showVendorDialog = true;
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"Error loading vendor: {ex.Message}");
        }
    }

    private async Task ViewVendor(int vendorId)
    {
        selectedVendorId = vendorId;
        showVendorDetailsDialog = true;
    }

    private async Task DeleteVendor(int vendorId)
    {
        var confirmed = await JSRuntime.InvokeAsync<bool>("confirm", "Are you sure you want to delete this vendor?");
        if (confirmed)
        {
            try
            {
                await VendorService.DeleteVendorAsync(vendorId);
                await LoadVendors();
                await JSRuntime.InvokeVoidAsync("alert", "Vendor deleted successfully!");
            }
            catch (Exception ex)
            {
                await JSRuntime.InvokeVoidAsync("alert", $"Error deleting vendor: {ex.Message}");
            }
        }
    }

    private async Task OnVendorSaved()
    {
        showVendorDialog = false;
        await LoadVendors();
        await JSRuntime.InvokeVoidAsync("alert", "Vendor saved successfully!");
    }

    private void OnVendorCancelled()
    {
        showVendorDialog = false;
    }

    public class CategoryOption
    {
        public string Text { get; set; } = string.Empty;
        public VendorCategory? Value { get; set; }
    }
}
