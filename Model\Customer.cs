using System.ComponentModel.DataAnnotations;

namespace JayERP.Model
{
    public class Customer : BaseEntity
    {
        [Required]
        [MaxLength(100)]
        public string Name { get; set; } = string.Empty;
        
        [Required]
        [MaxLength(200)]
        public string CompanyName { get; set; } = string.Empty;
        
        public CustomerCategory Category { get; set; }
        
        [Required]
        [MaxLength(500)]
        public string BillingAddress { get; set; } = string.Empty;
        
        [MaxLength(500)]
        public string ShippingAddress { get; set; } = string.Empty;
        
        [MaxLength(100)]
        public string City { get; set; } = string.Empty;
        
        [MaxLength(50)]
        public string State { get; set; } = string.Empty;
        
        [MaxLength(20)]
        public string PostalCode { get; set; } = string.Empty;
        
        [MaxLength(50)]
        public string Country { get; set; } = string.Empty;
        
        [Required]
        [EmailAddress]
        [MaxLength(100)]
        public string Email { get; set; } = string.Empty;
        
        [MaxLength(20)]
        public string Phone { get; set; } = string.Empty;
        
        [MaxLength(20)]
        public string Mobile { get; set; } = string.Empty;
        
        [MaxLength(100)]
        public string ContactPerson { get; set; } = string.Empty;
        
        [MaxLength(50)]
        public string TaxNumber { get; set; } = string.Empty;
        
        [MaxLength(50)]
        public string RegistrationNumber { get; set; } = string.Empty;
        
        public PaymentTerms PaymentTerms { get; set; }
        
        [Range(0, double.MaxValue)]
        public decimal CreditLimit { get; set; }
        
        [Range(0, double.MaxValue)]
        public decimal CurrentBalance { get; set; }
        
        [Range(0, 100)]
        public decimal DiscountPercentage { get; set; }
        
        [MaxLength(100)]
        public string SalesRepresentative { get; set; } = string.Empty;
        
        [MaxLength(1000)]
        public string Notes { get; set; } = string.Empty;
        
        // Navigation Properties
        public virtual ICollection<SalesOrder> SalesOrders { get; set; } = new List<SalesOrder>();
        public virtual ICollection<SalesInvoice> SalesInvoices { get; set; } = new List<SalesInvoice>();
        public virtual ICollection<CustomerPriceList> CustomerPriceLists { get; set; } = new List<CustomerPriceList>();
    }
}
