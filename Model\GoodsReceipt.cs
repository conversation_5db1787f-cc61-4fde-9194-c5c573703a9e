using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace JayERP.Model
{
    public class GoodsReceipt : BaseEntity
    {
        [Required]
        [MaxLength(50)]
        public string ReceiptNumber { get; set; } = string.Empty;
        
        [Required]
        public int PurchaseOrderId { get; set; }
        
        [Required]
        public int VendorId { get; set; }
        
        public DateTime ReceiptDate { get; set; } = DateTime.UtcNow;
        
        public GoodsReceiptStatus Status { get; set; } = GoodsReceiptStatus.Pending;
        
        [MaxLength(50)]
        public string VendorInvoiceNumber { get; set; } = string.Empty;
        
        public DateTime? VendorInvoiceDate { get; set; }
        
        [MaxLength(50)]
        public string DeliveryNoteNumber { get; set; } = string.Empty;
        
        [MaxLength(100)]
        public string ReceivedBy { get; set; } = string.Empty;
        
        [MaxLength(100)]
        public string InspectedBy { get; set; } = string.Empty;
        
        [MaxLength(1000)]
        public string Notes { get; set; } = string.Empty;
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalAmount { get; set; }
        
        // Navigation Properties
        [ForeignKey("PurchaseOrderId")]
        public virtual PurchaseOrder PurchaseOrder { get; set; } = null!;
        
        [ForeignKey("VendorId")]
        public virtual Vendor Vendor { get; set; } = null!;
        
        public virtual ICollection<GoodsReceiptItem> Items { get; set; } = new List<GoodsReceiptItem>();
    }
}
