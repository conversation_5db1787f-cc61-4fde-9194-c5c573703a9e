namespace JayERP.Model
{
    // Vendor & Procurement Enums
    public enum VendorCategory
    {
        Ingredients = 1,
        Packaging = 2,
        Supplies = 3,
        Services = 4
    }

    public enum PaymentTerms
    {
        Net30 = 1,
        Net15 = 2,
        Net7 = 3,
        AdvancePayment = 4,
        CashOnDelivery = 5,
        Immediate = 6
    }

    public enum PaymentMethod
    {
        Cash = 1,
        Check = 2,
        BankTransfer = 3,
        CreditCard = 4,
        OnlinePayment = 5
    }

    // Purchase & Procurement Enums
    public enum PurchaseOrderStatus
    {
        Draft = 1,
        PendingApproval = 2,
        Approved = 3,
        Sent = 4,
        PartiallyReceived = 5,
        FullyReceived = 6,
        Cancelled = 7,
        Closed = 8
    }

    public enum GoodsReceiptStatus
    {
        Pending = 1,
        PartiallyReceived = 2,
        FullyReceived = 3,
        Rejected = 4
    }

    // Inventory Enums
    public enum MaterialCategory
    {
        Sugar = 1,
        Flour = 2,
        Flavors = 3,
        Preservatives = 4,
        PackagingMaterials = 5,
        Dairy = 6,
        Nuts = 7,
        Chocolate = 8,
        Colors = 9,
        Other = 10
    }

    public enum UnitOfMeasure
    {
        Kilogram = 1,
        Gram = 2,
        Liter = 3,
        Milliliter = 4,
        Pieces = 5,
        Boxes = 6,
        Bags = 7,
        Bottles = 8,
        Cans = 9,
        Packets = 10
    }

    public enum StockMovementType
    {
        Receipt = 1,
        Issue = 2,
        Transfer = 3,
        Adjustment = 4,
        Return = 5,
        Wastage = 6
    }

    // Production Enums
    public enum ProductionOrderStatus
    {
        Planned = 1,
        Released = 2,
        InProgress = 3,
        Completed = 4,
        Cancelled = 5,
        OnHold = 6
    }

    public enum ProductCategory
    {
        Chocolates = 1,
        Candies = 2,
        Biscuits = 3,
        Cakes = 4,
        Cookies = 5,
        Gummies = 6,
        Toffees = 7,
        Other = 8
    }

    // Sales Enums
    public enum CustomerCategory
    {
        Distributor = 1,
        Retailer = 2,
        DirectCustomer = 3,
        Wholesaler = 4,
        OnlineCustomer = 5
    }

    public enum SalesOrderStatus
    {
        Draft = 1,
        Confirmed = 2,
        InProduction = 3,
        ReadyToShip = 4,
        Shipped = 5,
        Delivered = 6,
        Cancelled = 7,
        Returned = 8
    }

    public enum PackagingType
    {
        Retail = 1,
        Wholesale = 2,
        Bulk = 3,
        Gift = 4,
        Sample = 5
    }

    // Financial Enums
    public enum InvoiceStatus
    {
        Draft = 1,
        Sent = 2,
        Paid = 3,
        PartiallyPaid = 4,
        Overdue = 5,
        Cancelled = 6,
        Refunded = 7
    }

    public enum TransactionType
    {
        Sale = 1,
        Purchase = 2,
        Payment = 3,
        Receipt = 4,
        Adjustment = 5,
        Transfer = 6
    }

    // HR Enums
    public enum EmployeeType
    {
        FullTime = 1,
        PartTime = 2,
        Contract = 3,
        Temporary = 4,
        Intern = 5
    }

    public enum Department
    {
        Production = 1,
        Sales = 2,
        Purchase = 3,
        Finance = 4,
        HR = 5,
        Quality = 6,
        Warehouse = 7,
        Administration = 8
    }

    public enum AttendanceStatus
    {
        Present = 1,
        Absent = 2,
        HalfDay = 3,
        Late = 4,
        OnLeave = 5,
        Holiday = 6
    }

    public enum LeaveType
    {
        Casual = 1,
        Sick = 2,
        Annual = 3,
        Maternity = 4,
        Paternity = 5,
        Emergency = 6,
        Unpaid = 7
    }

    // General System Enums
    public enum ApprovalStatus
    {
        Pending = 1,
        Approved = 2,
        Rejected = 3,
        Cancelled = 4
    }

    public enum Priority
    {
        Low = 1,
        Medium = 2,
        High = 3,
        Critical = 4
    }

    public enum DocumentType
    {
        PurchaseOrder = 1,
        SalesOrder = 2,
        Invoice = 3,
        Receipt = 4,
        DeliveryNote = 5,
        ProductionOrder = 6,
        StockTransfer = 7,
        PaymentVoucher = 8
    }
}
