using System.ComponentModel.DataAnnotations;
using JayERP.Model;

namespace JayERP.DTOs
{
    public class VendorDto
    {
        public int Id { get; set; }
        
        [Required(ErrorMessage = "Vendor name is required")]
        [StringLength(100, ErrorMessage = "Name cannot exceed 100 characters")]
        public string Name { get; set; } = string.Empty;
        
        [Required(ErrorMessage = "Company name is required")]
        [StringLength(200, ErrorMessage = "Company name cannot exceed 200 characters")]
        public string CompanyName { get; set; } = string.Empty;
        
        [Required(ErrorMessage = "Category is required")]
        public VendorCategory Category { get; set; }
        
        [Required(ErrorMessage = "Address is required")]
        [StringLength(500, ErrorMessage = "Address cannot exceed 500 characters")]
        public string Address { get; set; } = string.Empty;
        
        [StringLength(100, ErrorMessage = "City cannot exceed 100 characters")]
        public string City { get; set; } = string.Empty;
        
        [StringLength(50, ErrorMessage = "State cannot exceed 50 characters")]
        public string State { get; set; } = string.Empty;
        
        [StringLength(20, ErrorMessage = "Postal code cannot exceed 20 characters")]
        public string PostalCode { get; set; } = string.Empty;
        
        [StringLength(50, ErrorMessage = "Country cannot exceed 50 characters")]
        public string Country { get; set; } = string.Empty;
        
        [Required(ErrorMessage = "Email is required")]
        [EmailAddress(ErrorMessage = "Invalid email format")]
        [StringLength(100, ErrorMessage = "Email cannot exceed 100 characters")]
        public string Email { get; set; } = string.Empty;
        
        [Phone(ErrorMessage = "Invalid phone format")]
        [StringLength(20, ErrorMessage = "Phone cannot exceed 20 characters")]
        public string Phone { get; set; } = string.Empty;
        
        [Phone(ErrorMessage = "Invalid mobile format")]
        [StringLength(20, ErrorMessage = "Mobile cannot exceed 20 characters")]
        public string Mobile { get; set; } = string.Empty;
        
        [StringLength(100, ErrorMessage = "Contact person cannot exceed 100 characters")]
        public string ContactPerson { get; set; } = string.Empty;
        
        [StringLength(50, ErrorMessage = "Tax number cannot exceed 50 characters")]
        public string TaxNumber { get; set; } = string.Empty;
        
        [StringLength(50, ErrorMessage = "Registration number cannot exceed 50 characters")]
        public string RegistrationNumber { get; set; } = string.Empty;
        
        [Required(ErrorMessage = "Payment terms are required")]
        public PaymentTerms PaymentTerms { get; set; }
        
        [Range(0, double.MaxValue, ErrorMessage = "Credit limit must be non-negative")]
        public decimal CreditLimit { get; set; }
        
        [Range(1, 5, ErrorMessage = "Quality rating must be between 1 and 5")]
        public int QualityRating { get; set; } = 3;
        
        [Range(1, 5, ErrorMessage = "Delivery rating must be between 1 and 5")]
        public int DeliveryRating { get; set; } = 3;
        
        [StringLength(1000, ErrorMessage = "Notes cannot exceed 1000 characters")]
        public string Notes { get; set; } = string.Empty;
        
        public bool IsActive { get; set; } = true;
        
        public DateTime CreatedDate { get; set; }
        public DateTime? ModifiedDate { get; set; }
        public string CreatedBy { get; set; } = string.Empty;
        public string ModifiedBy { get; set; } = string.Empty;
    }

    public class VendorCreateDto
    {
        [Required(ErrorMessage = "Vendor name is required")]
        [StringLength(100, ErrorMessage = "Name cannot exceed 100 characters")]
        public string Name { get; set; } = string.Empty;
        
        [Required(ErrorMessage = "Company name is required")]
        [StringLength(200, ErrorMessage = "Company name cannot exceed 200 characters")]
        public string CompanyName { get; set; } = string.Empty;
        
        [Required(ErrorMessage = "Category is required")]
        public VendorCategory Category { get; set; }
        
        [Required(ErrorMessage = "Address is required")]
        [StringLength(500, ErrorMessage = "Address cannot exceed 500 characters")]
        public string Address { get; set; } = string.Empty;
        
        [StringLength(100, ErrorMessage = "City cannot exceed 100 characters")]
        public string City { get; set; } = string.Empty;
        
        [StringLength(50, ErrorMessage = "State cannot exceed 50 characters")]
        public string State { get; set; } = string.Empty;
        
        [StringLength(20, ErrorMessage = "Postal code cannot exceed 20 characters")]
        public string PostalCode { get; set; } = string.Empty;
        
        [StringLength(50, ErrorMessage = "Country cannot exceed 50 characters")]
        public string Country { get; set; } = string.Empty;
        
        [Required(ErrorMessage = "Email is required")]
        [EmailAddress(ErrorMessage = "Invalid email format")]
        [StringLength(100, ErrorMessage = "Email cannot exceed 100 characters")]
        public string Email { get; set; } = string.Empty;
        
        [Phone(ErrorMessage = "Invalid phone format")]
        [StringLength(20, ErrorMessage = "Phone cannot exceed 20 characters")]
        public string Phone { get; set; } = string.Empty;
        
        [Phone(ErrorMessage = "Invalid mobile format")]
        [StringLength(20, ErrorMessage = "Mobile cannot exceed 20 characters")]
        public string Mobile { get; set; } = string.Empty;
        
        [StringLength(100, ErrorMessage = "Contact person cannot exceed 100 characters")]
        public string ContactPerson { get; set; } = string.Empty;
        
        [StringLength(50, ErrorMessage = "Tax number cannot exceed 50 characters")]
        public string TaxNumber { get; set; } = string.Empty;
        
        [StringLength(50, ErrorMessage = "Registration number cannot exceed 50 characters")]
        public string RegistrationNumber { get; set; } = string.Empty;
        
        [Required(ErrorMessage = "Payment terms are required")]
        public PaymentTerms PaymentTerms { get; set; }
        
        [Range(0, double.MaxValue, ErrorMessage = "Credit limit must be non-negative")]
        public decimal CreditLimit { get; set; }
        
        [Range(1, 5, ErrorMessage = "Quality rating must be between 1 and 5")]
        public int QualityRating { get; set; } = 3;
        
        [Range(1, 5, ErrorMessage = "Delivery rating must be between 1 and 5")]
        public int DeliveryRating { get; set; } = 3;
        
        [StringLength(1000, ErrorMessage = "Notes cannot exceed 1000 characters")]
        public string Notes { get; set; } = string.Empty;
    }

    public class VendorUpdateDto : VendorCreateDto
    {
        public int Id { get; set; }
        public bool IsActive { get; set; } = true;
    }

    public class VendorListDto
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string CompanyName { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string Phone { get; set; } = string.Empty;
        public string City { get; set; } = string.Empty;
        public string PaymentTerms { get; set; } = string.Empty;
        public decimal CreditLimit { get; set; }
        public int QualityRating { get; set; }
        public int DeliveryRating { get; set; }
        public bool IsActive { get; set; }
        public DateTime CreatedDate { get; set; }
    }

    public class VendorPerformanceDto
    {
        public int VendorId { get; set; }
        public string VendorName { get; set; } = string.Empty;
        public int TotalOrders { get; set; }
        public int OnTimeDeliveries { get; set; }
        public int QualityIssues { get; set; }
        public decimal OnTimeDeliveryPercentage { get; set; }
        public decimal QualityPercentage { get; set; }
        public decimal AverageDeliveryDays { get; set; }
        public decimal TotalPurchaseAmount { get; set; }
        public int QualityRating { get; set; }
        public int DeliveryRating { get; set; }
        public DateTime LastOrderDate { get; set; }
    }
}
