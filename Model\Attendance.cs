using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace JayERP.Model
{
    public class Attendance : BaseEntity
    {
        [Required]
        public int EmployeeId { get; set; }
        
        public DateTime AttendanceDate { get; set; }
        
        public TimeOnly? CheckInTime { get; set; }
        
        public TimeOnly? CheckOutTime { get; set; }
        
        public AttendanceStatus Status { get; set; }
        
        [Range(0, 24)]
        public decimal WorkingHours { get; set; }
        
        [Range(0, 24)]
        public decimal OvertimeHours { get; set; }
        
        [Range(0, 24)]
        public decimal BreakHours { get; set; }
        
        [MaxLength(100)]
        public string Shift { get; set; } = string.Empty;
        
        [MaxLength(500)]
        public string Notes { get; set; } = string.Empty;
        
        [MaxLength(100)]
        public string ApprovedBy { get; set; } = string.Empty;
        
        public DateTime? ApprovedDate { get; set; }
        
        public bool IsHoliday { get; set; } = false;
        
        public bool IsWeekend { get; set; } = false;
        
        // Navigation Properties
        [ForeignKey("EmployeeId")]
        public virtual Employee Employee { get; set; } = null!;
    }
}
