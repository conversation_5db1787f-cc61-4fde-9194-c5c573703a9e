@using JayERP.DTOs
@using JayERP.Services
@inject IVendorService VendorService
@inject IJSRuntime JSRuntime

@if (vendor != null)
{
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Vendor Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label fw-bold">Vendor Name:</label>
                                    <p class="mb-0">@vendor.Name</p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label fw-bold">Company Name:</label>
                                    <p class="mb-0">@vendor.CompanyName</p>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label fw-bold">Category:</label>
                                    <span class="badge bg-primary">@vendor.Category</span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label fw-bold">Payment Terms:</label>
                                    <span class="badge bg-info">@vendor.PaymentTerms</span>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <div class="mb-3">
                                    <label class="form-label fw-bold">Address:</label>
                                    <p class="mb-0">@vendor.Address</p>
                                    @if (!string.IsNullOrEmpty(vendor.City) || !string.IsNullOrEmpty(vendor.State))
                                    {
                                        <p class="mb-0">@vendor.City, @vendor.State @vendor.PostalCode</p>
                                    }
                                    @if (!string.IsNullOrEmpty(vendor.Country))
                                    {
                                        <p class="mb-0">@vendor.Country</p>
                                    }
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label fw-bold">Email:</label>
                                    <p class="mb-0">
                                        <a href="mailto:@vendor.Email">@vendor.Email</a>
                                    </p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label fw-bold">Phone:</label>
                                    <p class="mb-0">
                                        @if (!string.IsNullOrEmpty(vendor.Phone))
                                        {
                                            <a href="tel:@vendor.Phone">@vendor.Phone</a>
                                        }
                                        @if (!string.IsNullOrEmpty(vendor.Mobile))
                                        {
                                            <br /><a href="tel:@vendor.Mobile">@vendor.Mobile (Mobile)</a>
                                        }
                                    </p>
                                </div>
                            </div>
                        </div>

                        @if (!string.IsNullOrEmpty(vendor.ContactPerson))
                        {
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">Contact Person:</label>
                                        <p class="mb-0">@vendor.ContactPerson</p>
                                    </div>
                                </div>
                            </div>
                        }

                        @if (!string.IsNullOrEmpty(vendor.TaxNumber) || !string.IsNullOrEmpty(vendor.RegistrationNumber))
                        {
                            <div class="row">
                                <div class="col-md-6">
                                    @if (!string.IsNullOrEmpty(vendor.TaxNumber))
                                    {
                                        <div class="mb-3">
                                            <label class="form-label fw-bold">Tax Number:</label>
                                            <p class="mb-0">@vendor.TaxNumber</p>
                                        </div>
                                    }
                                </div>
                                <div class="col-md-6">
                                    @if (!string.IsNullOrEmpty(vendor.RegistrationNumber))
                                    {
                                        <div class="mb-3">
                                            <label class="form-label fw-bold">Registration Number:</label>
                                            <p class="mb-0">@vendor.RegistrationNumber</p>
                                        </div>
                                    }
                                </div>
                            </div>
                        }

                        @if (!string.IsNullOrEmpty(vendor.Notes))
                        {
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">Notes:</label>
                                        <p class="mb-0">@vendor.Notes</p>
                                    </div>
                                </div>
                            </div>
                        }
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                <div class="card mb-3">
                    <div class="card-header">
                        <h6 class="card-title mb-0">Performance Ratings</h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label fw-bold">Quality Rating:</label>
                            <div class="rating">
                                @for (int i = 1; i <= 5; i++)
                                {
                                    <i class="@(i <= vendor.QualityRating ? "fas fa-star text-warning" : "far fa-star text-muted")"></i>
                                }
                                <span class="ms-2">(@vendor.QualityRating/5)</span>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label fw-bold">Delivery Rating:</label>
                            <div class="rating">
                                @for (int i = 1; i <= 5; i++)
                                {
                                    <i class="@(i <= vendor.DeliveryRating ? "fas fa-star text-warning" : "far fa-star text-muted")"></i>
                                }
                                <span class="ms-2">(@vendor.DeliveryRating/5)</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card mb-3">
                    <div class="card-header">
                        <h6 class="card-title mb-0">Financial Information</h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label fw-bold">Credit Limit:</label>
                            <p class="mb-0 h5 text-success">@vendor.CreditLimit.ToString("C")</p>
                        </div>
                    </div>
                </div>

                @if (vendorPerformance != null)
                {
                    <div class="card">
                        <div class="card-header">
                            <h6 class="card-title mb-0">Performance Summary</h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-2">
                                <small class="text-muted">Total Orders:</small>
                                <span class="float-end fw-bold">@vendorPerformance.TotalOrders</span>
                            </div>
                            <div class="mb-2">
                                <small class="text-muted">On-Time Deliveries:</small>
                                <span class="float-end fw-bold">@vendorPerformance.OnTimeDeliveries</span>
                            </div>
                            <div class="mb-2">
                                <small class="text-muted">On-Time %:</small>
                                <span class="float-end fw-bold">@vendorPerformance.OnTimeDeliveryPercentage.ToString("F1")%</span>
                            </div>
                            <div class="mb-2">
                                <small class="text-muted">Total Purchase Amount:</small>
                                <span class="float-end fw-bold">@vendorPerformance.TotalPurchaseAmount.ToString("C")</span>
                            </div>
                            @if (vendorPerformance.LastOrderDate != DateTime.MinValue)
                            {
                                <div class="mb-2">
                                    <small class="text-muted">Last Order:</small>
                                    <span class="float-end fw-bold">@vendorPerformance.LastOrderDate.ToString("MMM dd, yyyy")</span>
                                </div>
                            }
                        </div>
                    </div>
                }

                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="card-title mb-0">Status</h6>
                    </div>
                    <div class="card-body">
                        <span class="badge @(vendor.IsActive ? "bg-success" : "bg-secondary") fs-6">
                            @(vendor.IsActive ? "Active" : "Inactive")
                        </span>
                        <div class="mt-2">
                            <small class="text-muted">Created: @vendor.CreatedDate.ToString("MMM dd, yyyy")</small>
                            @if (vendor.ModifiedDate.HasValue)
                            {
                                <br /><small class="text-muted">Modified: @vendor.ModifiedDate.Value.ToString("MMM dd, yyyy")</small>
                            }
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
}
else if (isLoading)
{
    <div class="text-center p-4">
        <div class="spinner-border" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
        <p class="mt-2">Loading vendor details...</p>
    </div>
}
else
{
    <div class="alert alert-warning">
        <i class="fas fa-exclamation-triangle"></i>
        Vendor not found or failed to load.
    </div>
}

@code {
    [Parameter] public int VendorId { get; set; }

    private VendorDto? vendor;
    private VendorPerformanceDto? vendorPerformance;
    private bool isLoading = true;

    protected override async Task OnParametersSetAsync()
    {
        if (VendorId > 0)
        {
            await LoadVendorDetails();
        }
    }

    private async Task LoadVendorDetails()
    {
        isLoading = true;
        try
        {
            vendor = await VendorService.GetVendorByIdAsync(VendorId);
            if (vendor != null)
            {
                vendorPerformance = await VendorService.GetVendorPerformanceAsync(VendorId);
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"Error loading vendor details: {ex.Message}");
        }
        finally
        {
            isLoading = false;
        }
    }
}
