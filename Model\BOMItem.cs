using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace JayERP.Model
{
    public class BOMItem : BaseEntity
    {
        [Required]
        public int BOMId { get; set; }
        
        [Required]
        public int MaterialId { get; set; }
        
        [Range(0, double.MaxValue)]
        public decimal Quantity { get; set; }
        
        public UnitOfMeasure Unit { get; set; }
        
        [Range(0, double.MaxValue)]
        public decimal UnitCost { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalCost { get; set; }
        
        [Range(0, 100)]
        public decimal WastagePercentage { get; set; } = 0;
        
        public bool IsOptional { get; set; } = false;
        
        public bool IsCritical { get; set; } = true;
        
        [MaxLength(500)]
        public string Notes { get; set; } = string.Empty;
        
        [Range(1, int.MaxValue)]
        public int SequenceNumber { get; set; }
        
        // Navigation Properties
        [ForeignKey("BOMId")]
        public virtual BOM BOM { get; set; } = null!;
        
        [ForeignKey("MaterialId")]
        public virtual Material Material { get; set; } = null!;
    }
}
