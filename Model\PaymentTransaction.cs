using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace JayERP.Model
{
    public class PaymentTransaction : BaseEntity
    {
        [Required]
        [MaxLength(50)]
        public string TransactionNumber { get; set; } = string.Empty;
        
        public TransactionType TransactionType { get; set; }
        
        public int? CustomerId { get; set; }
        
        public int? VendorId { get; set; }
        
        public int? SalesInvoiceId { get; set; }
        
        public int? PurchaseInvoiceId { get; set; }
        
        public DateTime TransactionDate { get; set; } = DateTime.UtcNow;
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal Amount { get; set; }
        
        public PaymentMethod PaymentMethod { get; set; }
        
        [MaxLength(100)]
        public string ReferenceNumber { get; set; } = string.Empty;
        
        [MaxLength(100)]
        public string BankName { get; set; } = string.Empty;
        
        [MaxLength(50)]
        public string CheckNumber { get; set; } = string.Empty;
        
        public DateTime? CheckDate { get; set; }
        
        [MaxLength(100)]
        public string ReceivedBy { get; set; } = string.Empty;
        
        [MaxLength(100)]
        public string ProcessedBy { get; set; } = string.Empty;
        
        [MaxLength(1000)]
        public string Notes { get; set; } = string.Empty;
        
        public bool IsReconciled { get; set; } = false;
        
        public DateTime? ReconciledDate { get; set; }
        
        // Navigation Properties
        [ForeignKey("CustomerId")]
        public virtual Customer? Customer { get; set; }
        
        [ForeignKey("VendorId")]
        public virtual Vendor? Vendor { get; set; }
        
        [ForeignKey("SalesInvoiceId")]
        public virtual SalesInvoice? SalesInvoice { get; set; }
        
        [ForeignKey("PurchaseInvoiceId")]
        public virtual PurchaseInvoice? PurchaseInvoice { get; set; }
    }
}
