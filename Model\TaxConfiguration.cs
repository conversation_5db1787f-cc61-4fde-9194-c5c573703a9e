using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace JayERP.Model
{
    public enum TaxType
    {
        GST = 1,
        VAT = 2,
        ServiceTax = 3,
        ExciseDuty = 4,
        CustomsDuty = 5,
        Other = 6
    }

    public class TaxConfiguration : BaseEntity
    {
        [Required]
        [MaxLength(50)]
        public string TaxCode { get; set; } = string.Empty;
        
        [Required]
        [MaxLength(100)]
        public string TaxName { get; set; } = string.Empty;
        
        public TaxType TaxType { get; set; }
        
        [Range(0, 100)]
        public decimal TaxRate { get; set; }
        
        public DateTime EffectiveDate { get; set; } = DateTime.UtcNow;
        
        public DateTime? ExpiryDate { get; set; }
        
        [MaxLength(500)]
        public string Description { get; set; } = string.Empty;
        
        public bool IsDefault { get; set; } = false;
        
        public bool IsActive { get; set; } = true;
        
        // For GST
        [Range(0, 100)]
        public decimal CGST { get; set; }
        
        [Range(0, 100)]
        public decimal SGST { get; set; }
        
        [Range(0, 100)]
        public decimal IGST { get; set; }
        
        [Range(0, 100)]
        public decimal CESS { get; set; }
        
        [MaxLength(1000)]
        public string Notes { get; set; } = string.Empty;
    }
}
