using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace JayERP.Model
{
    public class ProductionOrder : BaseEntity
    {
        [Required]
        [MaxLength(50)]
        public string OrderNumber { get; set; } = string.Empty;
        
        [Required]
        public int ProductId { get; set; }
        
        public int? BOMId { get; set; }
        
        public int? SalesOrderId { get; set; }
        
        [Range(0, double.MaxValue)]
        public decimal PlannedQuantity { get; set; }
        
        [Range(0, double.MaxValue)]
        public decimal ProducedQuantity { get; set; }
        
        [Range(0, double.MaxValue)]
        public decimal RejectedQuantity { get; set; }
        
        public DateTime PlannedStartDate { get; set; }
        
        public DateTime PlannedEndDate { get; set; }
        
        public DateTime? ActualStartDate { get; set; }
        
        public DateTime? ActualEndDate { get; set; }
        
        public ProductionOrderStatus Status { get; set; } = ProductionOrderStatus.Planned;
        
        public Priority Priority { get; set; } = Priority.Medium;
        
        [MaxLength(100)]
        public string BatchNumber { get; set; } = string.Empty;
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal PlannedCost { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal ActualCost { get; set; }
        
        [MaxLength(100)]
        public string AssignedTo { get; set; } = string.Empty;
        
        [MaxLength(100)]
        public string Supervisor { get; set; } = string.Empty;
        
        [MaxLength(1000)]
        public string Instructions { get; set; } = string.Empty;
        
        [MaxLength(1000)]
        public string QualityNotes { get; set; } = string.Empty;
        
        [MaxLength(1000)]
        public string Notes { get; set; } = string.Empty;
        
        // Navigation Properties
        [ForeignKey("ProductId")]
        public virtual Product Product { get; set; } = null!;
        
        [ForeignKey("BOMId")]
        public virtual BOM? BOM { get; set; }
        
        [ForeignKey("SalesOrderId")]
        public virtual SalesOrder? SalesOrder { get; set; }
        
        public virtual ICollection<ProductionOrderItem> Items { get; set; } = new List<ProductionOrderItem>();
        public virtual ICollection<ProductionCompletion> ProductionCompletions { get; set; } = new List<ProductionCompletion>();
    }
}
