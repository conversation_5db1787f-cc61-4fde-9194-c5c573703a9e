using JayERP.Model;
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations.Schema;

namespace JayERP.Data
{
    public class ApplicationDbContext(DbContextOptions<ApplicationDbContext> options) : IdentityDbContext<ApplicationUser>(options)
    {
        // Vendor & Procurement
        public DbSet<Vendor> Vendors { get; set; }
        public DbSet<PurchaseOrder> PurchaseOrders { get; set; }
        public DbSet<PurchaseOrderItem> PurchaseOrderItems { get; set; }
        public DbSet<GoodsReceipt> GoodsReceipts { get; set; }
        public DbSet<GoodsReceiptItem> GoodsReceiptItems { get; set; }
        public DbSet<PurchaseInvoice> PurchaseInvoices { get; set; }
        public DbSet<PurchaseInvoiceItem> PurchaseInvoiceItems { get; set; }

        // Inventory Management
        public DbSet<Material> Materials { get; set; }
        public DbSet<InventoryBalance> InventoryBalances { get; set; }
        public DbSet<StockMovement> StockMovements { get; set; }

        // Production Management
        public DbSet<Product> Products { get; set; }
        public DbSet<BOM> BOMs { get; set; }
        public DbSet<BOMItem> BOMItems { get; set; }
        public DbSet<ProductionOrder> ProductionOrders { get; set; }
        public DbSet<ProductionOrderItem> ProductionOrderItems { get; set; }
        public DbSet<ProductionCompletion> ProductionCompletions { get; set; }
        public DbSet<FinishedGoodsInventory> FinishedGoodsInventories { get; set; }

        // Sales & Customer Management
        public DbSet<Customer> Customers { get; set; }
        public DbSet<SalesOrder> SalesOrders { get; set; }
        public DbSet<SalesOrderItem> SalesOrderItems { get; set; }
        public DbSet<Delivery> Deliveries { get; set; }
        public DbSet<DeliveryItem> DeliveryItems { get; set; }
        public DbSet<SalesInvoice> SalesInvoices { get; set; }
        public DbSet<SalesInvoiceItem> SalesInvoiceItems { get; set; }
        public DbSet<CustomerPriceList> CustomerPriceLists { get; set; }

        // Financial Management
        public DbSet<PaymentTransaction> PaymentTransactions { get; set; }
        public DbSet<ChartOfAccounts> ChartOfAccounts { get; set; }
        public DbSet<JournalEntry> JournalEntries { get; set; }
        public DbSet<JournalEntryLine> JournalEntryLines { get; set; }

        // HR & Employee Management
        public DbSet<Employee> Employees { get; set; }
        public DbSet<Attendance> Attendances { get; set; }
        public DbSet<LeaveApplication> LeaveApplications { get; set; }
        public DbSet<Payroll> Payrolls { get; set; }

        // Master Data & Configuration
        public DbSet<Company> Companies { get; set; }
        public DbSet<NumberSeries> NumberSeries { get; set; }
        public DbSet<TaxConfiguration> TaxConfigurations { get; set; }
        public DbSet<SystemConfiguration> SystemConfigurations { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Configure cascade delete behavior to prevent cycles
            ConfigureCascadeDeletes(modelBuilder);

            // Configure indexes for performance
            ConfigureIndexes(modelBuilder);

            // Configure decimal precision
            ConfigureDecimalPrecision(modelBuilder);
        }

        private void ConfigureCascadeDeletes(ModelBuilder modelBuilder)
        {
            // Vendor relationships - Keep cascade for direct children only
            modelBuilder.Entity<PurchaseOrder>()
                .HasOne(po => po.Vendor)
                .WithMany(v => v.PurchaseOrders)
                .HasForeignKey(po => po.VendorId)
                .OnDelete(DeleteBehavior.Restrict); // Changed from Cascade

            modelBuilder.Entity<PurchaseInvoice>()
                .HasOne(pi => pi.Vendor)
                .WithMany(v => v.PurchaseInvoices)
                .HasForeignKey(pi => pi.VendorId)
                .OnDelete(DeleteBehavior.Restrict); // Changed from Cascade

            modelBuilder.Entity<GoodsReceipt>()
                .HasOne(gr => gr.Vendor)
                .WithMany()
                .HasForeignKey(gr => gr.VendorId)
                .OnDelete(DeleteBehavior.Restrict); // Changed from Cascade

            // Customer relationships
            modelBuilder.Entity<SalesOrder>()
                .HasOne(so => so.Customer)
                .WithMany(c => c.SalesOrders)
                .HasForeignKey(so => so.CustomerId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<SalesInvoice>()
                .HasOne(si => si.Customer)
                .WithMany(c => c.SalesInvoices)
                .HasForeignKey(si => si.CustomerId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<Delivery>()
                .HasOne(d => d.Customer)
                .WithMany()
                .HasForeignKey(d => d.CustomerId)
                .OnDelete(DeleteBehavior.Restrict);

            // Payment Transaction relationships
            modelBuilder.Entity<PaymentTransaction>()
                .HasOne(pt => pt.Customer)
                .WithMany()
                .HasForeignKey(pt => pt.CustomerId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<PaymentTransaction>()
                .HasOne(pt => pt.Vendor)
                .WithMany()
                .HasForeignKey(pt => pt.VendorId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<PaymentTransaction>()
                .HasOne(pt => pt.SalesInvoice)
                .WithMany(si => si.PaymentTransactions)
                .HasForeignKey(pt => pt.SalesInvoiceId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<PaymentTransaction>()
                .HasOne(pt => pt.PurchaseInvoice)
                .WithMany(pi => pi.PaymentTransactions)
                .HasForeignKey(pt => pt.PurchaseInvoiceId)
                .OnDelete(DeleteBehavior.Restrict);

            // Employee self-referencing relationship
            modelBuilder.Entity<Employee>()
                .HasOne(e => e.ReportingManager)
                .WithMany(e => e.Subordinates)
                .HasForeignKey(e => e.ReportingManagerId)
                .OnDelete(DeleteBehavior.Restrict);

            // Chart of Accounts self-referencing relationship
            modelBuilder.Entity<ChartOfAccounts>()
                .HasOne(c => c.ParentAccount)
                .WithMany(c => c.ChildAccounts)
                .HasForeignKey(c => c.ParentAccountId)
                .OnDelete(DeleteBehavior.Restrict);

            // Additional cascade delete configurations to prevent cycles
            modelBuilder.Entity<GoodsReceiptItem>()
                .HasOne(gri => gri.PurchaseOrderItem)
                .WithMany(poi => poi.GoodsReceiptItems)
                .HasForeignKey(gri => gri.PurchaseOrderItemId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<GoodsReceiptItem>()
                .HasOne(gri => gri.Material)
                .WithMany()
                .HasForeignKey(gri => gri.MaterialId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<PurchaseOrderItem>()
                .HasOne(poi => poi.Material)
                .WithMany(m => m.PurchaseOrderItems)
                .HasForeignKey(poi => poi.MaterialId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<BOMItem>()
                .HasOne(bi => bi.Material)
                .WithMany(m => m.BOMItems)
                .HasForeignKey(bi => bi.MaterialId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<ProductionOrderItem>()
                .HasOne(poi => poi.Material)
                .WithMany()
                .HasForeignKey(poi => poi.MaterialId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<SalesOrderItem>()
                .HasOne(soi => soi.Product)
                .WithMany(p => p.SalesOrderItems)
                .HasForeignKey(soi => soi.ProductId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<DeliveryItem>()
                .HasOne(di => di.Product)
                .WithMany()
                .HasForeignKey(di => di.ProductId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<DeliveryItem>()
                .HasOne(di => di.SalesOrderItem)
                .WithMany(soi => soi.DeliveryItems)
                .HasForeignKey(di => di.SalesOrderItemId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<SalesInvoiceItem>()
                .HasOne(sii => sii.Product)
                .WithMany()
                .HasForeignKey(sii => sii.ProductId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<PurchaseInvoiceItem>()
                .HasOne(pii => pii.Material)
                .WithMany()
                .HasForeignKey(pii => pii.MaterialId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<ProductionOrder>()
                .HasOne(po => po.SalesOrder)
                .WithMany(so => so.ProductionOrders)
                .HasForeignKey(po => po.SalesOrderId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<ProductionOrder>()
                .HasOne(po => po.BOM)
                .WithMany()
                .HasForeignKey(po => po.BOMId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<SalesInvoice>()
                .HasOne(si => si.SalesOrder)
                .WithMany()
                .HasForeignKey(si => si.SalesOrderId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<SalesInvoice>()
                .HasOne(si => si.Delivery)
                .WithMany()
                .HasForeignKey(si => si.DeliveryId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<PurchaseInvoice>()
                .HasOne(pi => pi.PurchaseOrder)
                .WithMany()
                .HasForeignKey(pi => pi.PurchaseOrderId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<PurchaseInvoice>()
                .HasOne(pi => pi.GoodsReceipt)
                .WithMany()
                .HasForeignKey(pi => pi.GoodsReceiptId)
                .OnDelete(DeleteBehavior.Restrict);
        }

        private void ConfigureIndexes(ModelBuilder modelBuilder)
        {
            // Vendor indexes
            modelBuilder.Entity<Vendor>()
                .HasIndex(v => v.Email)
                .IsUnique();

            modelBuilder.Entity<Vendor>()
                .HasIndex(v => v.TaxNumber);

            // Customer indexes
            modelBuilder.Entity<Customer>()
                .HasIndex(c => c.Email)
                .IsUnique();

            modelBuilder.Entity<Customer>()
                .HasIndex(c => c.TaxNumber);

            // Material indexes
            modelBuilder.Entity<Material>()
                .HasIndex(m => m.Code)
                .IsUnique();

            // Product indexes
            modelBuilder.Entity<Product>()
                .HasIndex(p => p.Code)
                .IsUnique();

            modelBuilder.Entity<Product>()
                .HasIndex(p => p.Barcode);

            // Employee indexes
            modelBuilder.Entity<Employee>()
                .HasIndex(e => e.EmployeeCode)
                .IsUnique();

            modelBuilder.Entity<Employee>()
                .HasIndex(e => e.Email)
                .IsUnique();

            // Document number indexes
            modelBuilder.Entity<PurchaseOrder>()
                .HasIndex(po => po.OrderNumber)
                .IsUnique();

            modelBuilder.Entity<SalesOrder>()
                .HasIndex(so => so.OrderNumber)
                .IsUnique();

            modelBuilder.Entity<SalesInvoice>()
                .HasIndex(si => si.InvoiceNumber)
                .IsUnique();

            modelBuilder.Entity<PurchaseInvoice>()
                .HasIndex(pi => pi.InvoiceNumber)
                .IsUnique();

            // Chart of Accounts indexes
            modelBuilder.Entity<ChartOfAccounts>()
                .HasIndex(c => c.AccountCode)
                .IsUnique();

            // System Configuration indexes
            modelBuilder.Entity<SystemConfiguration>()
                .HasIndex(sc => sc.ConfigKey)
                .IsUnique();
        }

        private void ConfigureDecimalPrecision(ModelBuilder modelBuilder)
        {
            // Configure decimal precision for all decimal properties
            foreach (var entityType in modelBuilder.Model.GetEntityTypes())
            {
                foreach (var property in entityType.GetProperties())
                {
                    if (property.ClrType == typeof(decimal) || property.ClrType == typeof(decimal?))
                    {
                        property.SetColumnType("decimal(18,2)");
                    }
                }
            }
        }
    }
}
