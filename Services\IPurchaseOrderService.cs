using JayERP.DTOs;
using JayERP.Model;

namespace JayERP.Services
{
    public interface IPurchaseOrderService
    {
        Task<IEnumerable<PurchaseOrderListDto>> GetAllPurchaseOrdersAsync();
        Task<PurchaseOrderDto?> GetPurchaseOrderByIdAsync(int id);
        Task<PurchaseOrderDto> CreatePurchaseOrderAsync(PurchaseOrderCreateDto createDto, string createdBy);
        Task<PurchaseOrderDto> UpdatePurchaseOrderAsync(PurchaseOrderUpdateDto updateDto, string modifiedBy);
        Task<bool> DeletePurchaseOrderAsync(int id);
        Task<bool> ApprovePurchaseOrderAsync(int id, string approvedBy, string comments = "");
        Task<bool> RejectPurchaseOrderAsync(int id, string rejectedBy, string reason);
        Task<IEnumerable<PurchaseOrderListDto>> GetPendingApprovalsAsync();
        Task<IEnumerable<PurchaseOrderListDto>> GetPurchaseOrdersByVendorAsync(int vendorId);
        Task<IEnumerable<PurchaseOrderListDto>> GetPurchaseOrdersByStatusAsync(PurchaseOrderStatus status);
        Task<IEnumerable<PurchaseOrderListDto>> SearchPurchaseOrdersAsync(string searchTerm);
        Task<string> GenerateOrderNumberAsync();
        Task<bool> CanDeletePurchaseOrderAsync(int id);
        Task<bool> CanEditPurchaseOrderAsync(int id);
    }
}
