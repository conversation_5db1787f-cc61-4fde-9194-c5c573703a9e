using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace JayERP.Model
{
    public class DeliveryItem : BaseEntity
    {
        [Required]
        public int DeliveryId { get; set; }
        
        [Required]
        public int SalesOrderItemId { get; set; }
        
        [Required]
        public int ProductId { get; set; }
        
        [Range(0, double.MaxValue)]
        public decimal OrderedQuantity { get; set; }
        
        [Range(0, double.MaxValue)]
        public decimal DeliveredQuantity { get; set; }
        
        [Range(0, double.MaxValue)]
        public decimal UnitPrice { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal LineTotal { get; set; }
        
        [MaxLength(50)]
        public string BatchNumber { get; set; } = string.Empty;
        
        public DateTime? ExpiryDate { get; set; }
        
        [MaxLength(100)]
        public string PackagingDetails { get; set; } = string.Empty;
        
        [MaxLength(500)]
        public string Notes { get; set; } = string.Empty;
        
        // Navigation Properties
        [ForeignKey("DeliveryId")]
        public virtual Delivery Delivery { get; set; } = null!;
        
        [ForeignKey("SalesOrderItemId")]
        public virtual SalesOrderItem SalesOrderItem { get; set; } = null!;
        
        [ForeignKey("ProductId")]
        public virtual Product Product { get; set; } = null!;
    }
}
