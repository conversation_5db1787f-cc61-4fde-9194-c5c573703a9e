using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace JayERP.Model
{
    public class InventoryBalance : BaseEntity
    {
        [Required]
        public int MaterialId { get; set; }
        
        [Required]
        [MaxLength(100)]
        public string Location { get; set; } = string.Empty;
        
        [MaxLength(50)]
        public string BatchNumber { get; set; } = string.Empty;
        
        public DateTime? ExpiryDate { get; set; }
        
        [Range(0, double.MaxValue)]
        public decimal QuantityOnHand { get; set; }
        
        [Range(0, double.MaxValue)]
        public decimal ReservedQuantity { get; set; }
        
        [Range(0, double.MaxValue)]
        public decimal AvailableQuantity { get; set; }
        
        [Range(0, double.MaxValue)]
        public decimal UnitCost { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalValue { get; set; }
        
        public DateTime LastMovementDate { get; set; } = DateTime.UtcNow;
        
        // Navigation Properties
        [ForeignKey("MaterialId")]
        public virtual Material Material { get; set; } = null!;
    }
}
