using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace JayERP.Model
{
    public class ProductionOrderItem : BaseEntity
    {
        [Required]
        public int ProductionOrderId { get; set; }
        
        [Required]
        public int MaterialId { get; set; }
        
        [Range(0, double.MaxValue)]
        public decimal PlannedQuantity { get; set; }
        
        [Range(0, double.MaxValue)]
        public decimal IssuedQuantity { get; set; }
        
        [Range(0, double.MaxValue)]
        public decimal ConsumedQuantity { get; set; }
        
        [Range(0, double.MaxValue)]
        public decimal ReturnedQuantity { get; set; }
        
        [Range(0, double.MaxValue)]
        public decimal UnitCost { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalCost { get; set; }
        
        [MaxLength(50)]
        public string BatchNumber { get; set; } = string.Empty;
        
        public DateTime? IssueDate { get; set; }
        
        [MaxLength(500)]
        public string Notes { get; set; } = string.Empty;
        
        // Navigation Properties
        [ForeignKey("ProductionOrderId")]
        public virtual ProductionOrder ProductionOrder { get; set; } = null!;
        
        [ForeignKey("MaterialId")]
        public virtual Material Material { get; set; } = null!;
    }
}
