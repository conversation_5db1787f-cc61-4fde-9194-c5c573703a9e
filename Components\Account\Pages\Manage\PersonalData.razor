﻿@page "/Account/Manage/PersonalData"

@inject IdentityUserAccessor UserAccessor

<PageTitle>Personal Data</PageTitle>

<StatusMessage />
<h3>Personal Data</h3>

<FluentGrid>
    <FluentGridItem xs="12" sm="6">
        <p>Your account contains personal data that you have given us. This page allows you to download or delete that data.</p>
        <p>
            <strong>Deleting this data will permanently remove your account, and this cannot be recovered.</strong>
        </p>
        <form action="Account/Manage/DownloadPersonalData" method="post">
            <AntiforgeryToken />
            <FluentButton Type="ButtonType.Submit" Appearance="Appearance.Accent" Style="width:20%;">Download</FluentButton>
        </form>
        
        <hr />
        
        <p>
            <FluentAnchor Href="Account/Manage/DeletePersonalData" Style="width:20%;">Delete</FluentAnchor>
        </p>
    </FluentGridItem>
</FluentGrid>

@code {
    [CascadingParameter]
    private HttpContext HttpContext { get; set; } = default!;

    protected override async Task OnInitializedAsync()
    {
        _ = await UserAccessor.GetRequiredUserAsync(HttpContext);
    }
}
