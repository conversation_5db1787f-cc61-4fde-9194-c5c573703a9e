using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace JayERP.Model
{
    public class PurchaseInvoiceItem : BaseEntity
    {
        [Required]
        public int PurchaseInvoiceId { get; set; }
        
        [Required]
        public int MaterialId { get; set; }
        
        [Range(0, double.MaxValue)]
        public decimal Quantity { get; set; }
        
        [Range(0, double.MaxValue)]
        public decimal UnitPrice { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal LineTotal { get; set; }
        
        [Range(0, 100)]
        public decimal TaxRate { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal TaxAmount { get; set; }
        
        [Range(0, 100)]
        public decimal DiscountRate { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal DiscountAmount { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal NetAmount { get; set; }
        
        [MaxLength(500)]
        public string Description { get; set; } = string.Empty;
        
        // Navigation Properties
        [ForeignKey("PurchaseInvoiceId")]
        public virtual PurchaseInvoice PurchaseInvoice { get; set; } = null!;
        
        [ForeignKey("MaterialId")]
        public virtual Material Material { get; set; } = null!;
    }
}
