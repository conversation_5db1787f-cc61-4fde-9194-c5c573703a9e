@using JayERP.DTOs
@using JayERP.Services
@using JayERP.Model
@using Syncfusion.Blazor.Buttons
@using Syncfusion.Blazor.Inputs
@using Syncfusion.Blazor.DropDowns
@using Syncfusion.Blazor.Calendars
@using Syncfusion.Blazor.Grids

@inject IPurchaseInvoiceService PurchaseInvoiceService
@inject IVendorService VendorService
@inject IGoodsReceiptService GoodsReceiptService
@inject IJSRuntime JSRuntime

<div class="container-fluid">
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Purchase Invoice Information</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Invoice Number <span class="text-danger">*</span></label>
                                <SfTextBox @bind-Value="purchaseInvoiceModel.InvoiceNumber"
                                          Placeholder="Auto-generated"
                                          Readonly="true"
                                          CssClass="@GetValidationClass("InvoiceNumber")">
                                </SfTextBox>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Vendor <span class="text-danger">*</span></label>
                                <SfDropDownList TValue="int" TItem="VendorListDto"
                                               @bind-Value="purchaseInvoiceModel.VendorId"
                                               DataSource="vendors"
                                               Placeholder="Select Vendor"
                                               CssClass="@GetValidationClass("VendorId")">
                                    <DropDownListFieldSettings Text="Name" Value="Id"></DropDownListFieldSettings>
                                </SfDropDownList>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Vendor Invoice Number <span class="text-danger">*</span></label>
                                <SfTextBox @bind-Value="purchaseInvoiceModel.VendorInvoiceNumber"
                                          Placeholder="Enter vendor invoice number"
                                          CssClass="@GetValidationClass("VendorInvoiceNumber")">
                                </SfTextBox>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Goods Receipt</label>
                                <SfDropDownList TValue="int?" TItem="GoodsReceiptListDto"
                                               @bind-Value="purchaseInvoiceModel.GoodsReceiptId"
                                               DataSource="goodsReceipts"
                                               Placeholder="Select Goods Receipt"
                                               AllowClear="true"
                                               ValueChange="OnGoodsReceiptChanged">
                                    <DropDownListFieldSettings Text="ReceiptNumber" Value="Id"></DropDownListFieldSettings>
                                </SfDropDownList>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Invoice Date <span class="text-danger">*</span></label>
                                <SfDatePicker @bind-Value="purchaseInvoiceModel.InvoiceDate"
                                             Format="dd/MM/yyyy"
                                             CssClass="@GetValidationClass("InvoiceDate")">
                                </SfDatePicker>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Due Date <span class="text-danger">*</span></label>
                                <SfDatePicker @bind-Value="purchaseInvoiceModel.DueDate"
                                             Format="dd/MM/yyyy"
                                             CssClass="@GetValidationClass("DueDate")">
                                </SfDatePicker>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Payment Terms <span class="text-danger">*</span></label>
                                <SfDropDownList TValue="PaymentTerms" TItem="PaymentTermsOption"
                                               @bind-Value="purchaseInvoiceModel.PaymentTerms"
                                               DataSource="paymentTermsOptions"
                                               Placeholder="Select Payment Terms"
                                               CssClass="@GetValidationClass("PaymentTerms")"
                                               ValueChange="OnPaymentTermsChanged">
                                    <DropDownListFieldSettings Text="Text" Value="Value"></DropDownListFieldSettings>
                                </SfDropDownList>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Status</label>
                                <SfDropDownList TValue="InvoiceStatus" TItem="StatusOption"
                                               @bind-Value="purchaseInvoiceModel.Status"
                                               DataSource="statusOptions"
                                               Enabled="@(PurchaseInvoice != null)">
                                    <DropDownListFieldSettings Text="Text" Value="Value"></DropDownListFieldSettings>
                                </SfDropDownList>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-12">
                            <div class="mb-3">
                                <label class="form-label">Notes</label>
                                <SfTextBox @bind-Value="purchaseInvoiceModel.Notes"
                                          Placeholder="Additional notes..."
                                          Multiline="true">
                                </SfTextBox>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Purchase Invoice Items -->
            <div class="card mt-3">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">Invoice Items</h5>
                    <SfButton @onclick="AddNewItem"
                              CssClass="e-small e-primary"
                              IconCss="e-icons e-plus">
                        Add Item
                    </SfButton>
                </div>
                <div class="card-body">
                    @if (purchaseInvoiceModel.Items.Any())
                    {
                        <SfGrid DataSource="@purchaseInvoiceModel.Items"
                                AllowPaging="false"
                                Height="300px">
                            <GridColumns>
                                <GridColumn HeaderText="Material" Width="200">
                                    <Template>
                                        @{
                                            var item = (context as PurchaseInvoiceItemCreateDto);
                                            var materialName = GetMaterialName(item.MaterialId);
                                        }
                                        @materialName
                                    </Template>
                                </GridColumn>
                                <GridColumn Field="Quantity" HeaderText="Quantity" Width="100" Format="N2" TextAlign="TextAlign.Right"></GridColumn>
                                <GridColumn Field="UnitPrice" HeaderText="Unit Price" Width="120" Format="C2" TextAlign="TextAlign.Right"></GridColumn>
                                <GridColumn HeaderText="Line Total" Width="120" TextAlign="TextAlign.Right">
                                    <Template>
                                        @{
                                            var item = (context as PurchaseInvoiceItemCreateDto);
                                            var lineTotal = item.Quantity * item.UnitPrice;
                                        }
                                        @lineTotal.ToString("C2")
                                    </Template>
                                </GridColumn>
                                <GridColumn Field="TaxRate" HeaderText="Tax %" Width="80" Format="N1" TextAlign="TextAlign.Right"></GridColumn>
                                <GridColumn Field="DiscountRate" HeaderText="Disc %" Width="80" Format="N1" TextAlign="TextAlign.Right"></GridColumn>
                                <GridColumn HeaderText="Actions" Width="100" TextAlign="TextAlign.Center">
                                    <Template>
                                        @{
                                            var item = (context as PurchaseInvoiceItemCreateDto);
                                            var index = purchaseInvoiceModel.Items.IndexOf(item);
                                        }
                                        <SfButton @onclick="() => RemoveItem(index)"
                                                 CssClass="e-small e-danger"
                                                 IconCss="e-icons e-delete">
                                        </SfButton>
                                    </Template>
                                </GridColumn>
                            </GridColumns>
                        </SfGrid>
                    }
                    else
                    {
                        <div class="text-center py-4">
                            <p class="text-muted">No items added yet. Click "Add Item" to get started.</p>
                        </div>
                    }
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <!-- Invoice Summary -->
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">Invoice Summary</h6>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between mb-2">
                        <span>Subtotal:</span>
                        <span>@CalculateSubtotal().ToString("C2")</span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span>Discount:</span>
                        <span>@CalculateDiscount().ToString("C2")</span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span>Tax:</span>
                        <span>@CalculateTax().ToString("C2")</span>
                    </div>
                    <hr>
                    <div class="d-flex justify-content-between fw-bold">
                        <span>Total:</span>
                        <span>@CalculateTotal().ToString("C2")</span>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="card mt-3">
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <SfButton @onclick="SavePurchaseInvoice"
                                  CssClass="e-primary"
                                  IconCss="e-icons e-save"
                                  Disabled="@isSubmitting">
                            @(isSubmitting ? "Saving..." : "Save Purchase Invoice")
                        </SfButton>
                        <SfButton @onclick="OnCancelClick"
                                  CssClass="e-outline">
                            Cancel
                        </SfButton>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    [Parameter] public PurchaseInvoiceDto? PurchaseInvoice { get; set; }
    [Parameter] public EventCallback OnSave { get; set; }
    [Parameter] public EventCallback OnCancel { get; set; }

    private PurchaseInvoiceCreateDto purchaseInvoiceModel = new();
    private List<VendorListDto> vendors = new();
    private List<GoodsReceiptListDto> goodsReceipts = new();
    private bool isSubmitting = false;

    private List<PaymentTermsOption> paymentTermsOptions = new()
    {
        new PaymentTermsOption { Text = "Net 30", Value = PaymentTerms.Net30 },
        new PaymentTermsOption { Text = "Net 15", Value = PaymentTerms.Net15 },
        new PaymentTermsOption { Text = "Net 7", Value = PaymentTerms.Net7 },
        new PaymentTermsOption { Text = "Advance Payment", Value = PaymentTerms.AdvancePayment },
        new PaymentTermsOption { Text = "Cash on Delivery", Value = PaymentTerms.CashOnDelivery },
        new PaymentTermsOption { Text = "Immediate", Value = PaymentTerms.Immediate }
    };

    private List<StatusOption> statusOptions = new()
    {
        new StatusOption { Text = "Draft", Value = InvoiceStatus.Draft },
        new StatusOption { Text = "Sent", Value = InvoiceStatus.Sent },
        new StatusOption { Text = "Paid", Value = InvoiceStatus.Paid },
        new StatusOption { Text = "Cancelled", Value = InvoiceStatus.Cancelled }
    };

    protected override async Task OnInitializedAsync()
    {
        await LoadVendors();
        await LoadGoodsReceipts();
        if (PurchaseInvoice == null)
        {
            purchaseInvoiceModel.InvoiceNumber = await PurchaseInvoiceService.GenerateInvoiceNumberAsync();
            purchaseInvoiceModel.DueDate = DateTime.UtcNow.AddDays(30); // Default 30 days
        }
    }

    protected override void OnParametersSet()
    {
        if (PurchaseInvoice != null)
        {
            // Map existing purchase invoice to create model
            purchaseInvoiceModel = new PurchaseInvoiceCreateDto
            {
                VendorId = PurchaseInvoice.VendorId,
                PurchaseOrderId = PurchaseInvoice.PurchaseOrderId,
                GoodsReceiptId = PurchaseInvoice.GoodsReceiptId,
                InvoiceDate = PurchaseInvoice.InvoiceDate,
                DueDate = PurchaseInvoice.DueDate,
                Status = PurchaseInvoice.Status,
                VendorInvoiceNumber = PurchaseInvoice.VendorInvoiceNumber,
                PaymentTerms = PurchaseInvoice.PaymentTerms,
                Notes = PurchaseInvoice.Notes,
                Items = PurchaseInvoice.Items.Select(item => new PurchaseInvoiceItemCreateDto
                {
                    MaterialId = item.MaterialId,
                    Quantity = item.Quantity,
                    UnitPrice = item.UnitPrice,
                    TaxRate = item.TaxRate,
                    DiscountRate = item.DiscountRate,
                    Description = item.Description
                }).ToList()
            };
        }
        else
        {
            purchaseInvoiceModel = new PurchaseInvoiceCreateDto();
        }
    }

    private async Task LoadVendors()
    {
        try
        {
            vendors = (await VendorService.GetAllVendorsAsync()).ToList();
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"Error loading vendors: {ex.Message}");
        }
    }

    private async Task LoadGoodsReceipts()
    {
        try
        {
            // Load completed goods receipts
            var allReceipts = await GoodsReceiptService.GetGoodsReceiptsByStatusAsync(GoodsReceiptStatus.FullyReceived);
            goodsReceipts = allReceipts.ToList();
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"Error loading goods receipts: {ex.Message}");
        }
    }

    private async Task OnGoodsReceiptChanged(ChangeEventArgs<int?, GoodsReceiptListDto> args)
    {
        if (args.Value.HasValue)
        {
            try
            {
                var goodsReceipt = await GoodsReceiptService.GetGoodsReceiptByIdAsync(args.Value.Value);
                if (goodsReceipt != null)
                {
                    purchaseInvoiceModel.VendorId = goodsReceipt.VendorId;
                    purchaseInvoiceModel.PurchaseOrderId = goodsReceipt.PurchaseOrderId;

                    // Load items from goods receipt
                    purchaseInvoiceModel.Items = goodsReceipt.Items.Select(item => new PurchaseInvoiceItemCreateDto
                    {
                        MaterialId = item.MaterialId,
                        Quantity = item.AcceptedQuantity,
                        UnitPrice = item.UnitPrice,
                        TaxRate = 10, // Default tax rate
                        DiscountRate = 0,
                        Description = $"From GR: {goodsReceipt.ReceiptNumber}"
                    }).ToList();
                }
            }
            catch (Exception ex)
            {
                await JSRuntime.InvokeVoidAsync("alert", $"Error loading goods receipt: {ex.Message}");
            }
        }
    }

    private void OnPaymentTermsChanged(ChangeEventArgs<PaymentTerms, PaymentTermsOption> args)
    {
        // Update due date based on payment terms
        var days = args.Value switch
        {
            PaymentTerms.Net30 => 30,
            PaymentTerms.Net15 => 15,
            PaymentTerms.Net7 => 7,
            PaymentTerms.Immediate => 0,
            _ => 30
        };
        purchaseInvoiceModel.DueDate = purchaseInvoiceModel.InvoiceDate.AddDays(days);
    }

    private void AddNewItem()
    {
        purchaseInvoiceModel.Items.Add(new PurchaseInvoiceItemCreateDto
        {
            Quantity = 1,
            UnitPrice = 0,
            TaxRate = 10,
            DiscountRate = 0
        });
    }

    private void RemoveItem(int index)
    {
        if (index >= 0 && index < purchaseInvoiceModel.Items.Count)
        {
            purchaseInvoiceModel.Items.RemoveAt(index);
        }
    }

    private string GetMaterialName(int materialId)
    {
        // This would typically come from a material service
        return $"Material {materialId}";
    }

    private decimal CalculateSubtotal()
    {
        return purchaseInvoiceModel.Items.Sum(item => item.Quantity * item.UnitPrice);
    }

    private decimal CalculateDiscount()
    {
        return purchaseInvoiceModel.Items.Sum(item =>
            (item.Quantity * item.UnitPrice) * (item.DiscountRate / 100));
    }

    private decimal CalculateTax()
    {
        return purchaseInvoiceModel.Items.Sum(item =>
        {
            var lineTotal = item.Quantity * item.UnitPrice;
            var discountAmount = lineTotal * (item.DiscountRate / 100);
            var taxableAmount = lineTotal - discountAmount;
            return taxableAmount * (item.TaxRate / 100);
        });
    }

    private decimal CalculateTotal()
    {
        return CalculateSubtotal() - CalculateDiscount() + CalculateTax();
    }

    private async Task SavePurchaseInvoice()
    {
        if (!ValidateForm()) return;

        isSubmitting = true;
        try
        {
            if (PurchaseInvoice != null)
            {
                var updateDto = new PurchaseInvoiceUpdateDto
                {
                    Id = PurchaseInvoice.Id,
                    VendorId = purchaseInvoiceModel.VendorId,
                    PurchaseOrderId = purchaseInvoiceModel.PurchaseOrderId,
                    GoodsReceiptId = purchaseInvoiceModel.GoodsReceiptId,
                    InvoiceDate = purchaseInvoiceModel.InvoiceDate,
                    DueDate = purchaseInvoiceModel.DueDate,
                    Status = purchaseInvoiceModel.Status,
                    VendorInvoiceNumber = purchaseInvoiceModel.VendorInvoiceNumber,
                    PaymentTerms = purchaseInvoiceModel.PaymentTerms,
                    Notes = purchaseInvoiceModel.Notes,
                    SubTotal = CalculateSubtotal(),
                    TaxAmount = CalculateTax(),
                    DiscountAmount = CalculateDiscount(),
                    TotalAmount = CalculateTotal(),
                    Items = purchaseInvoiceModel.Items
                };

                await PurchaseInvoiceService.UpdatePurchaseInvoiceAsync(updateDto, "CurrentUser");
            }
            else
            {
                await PurchaseInvoiceService.CreatePurchaseInvoiceAsync(purchaseInvoiceModel, "CurrentUser");
            }

            await OnSave.InvokeAsync();
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"Error saving purchase invoice: {ex.Message}");
        }
        finally
        {
            isSubmitting = false;
        }
    }

    private bool ValidateForm()
    {
        if (purchaseInvoiceModel.VendorId <= 0)
        {
            JSRuntime.InvokeVoidAsync("alert", "Please select a vendor.");
            return false;
        }

        if (string.IsNullOrWhiteSpace(purchaseInvoiceModel.VendorInvoiceNumber))
        {
            JSRuntime.InvokeVoidAsync("alert", "Please enter vendor invoice number.");
            return false;
        }

        if (!purchaseInvoiceModel.Items.Any())
        {
            JSRuntime.InvokeVoidAsync("alert", "Please add at least one item.");
            return false;
        }

        return true;
    }

    private async Task OnCancelClick()
    {
        await OnCancel.InvokeAsync();
    }

    private string GetValidationClass(string fieldName)
    {
        return string.Empty;
    }

    public class PaymentTermsOption
    {
        public string Text { get; set; } = string.Empty;
        public PaymentTerms Value { get; set; }
    }

    public class StatusOption
    {
        public string Text { get; set; } = string.Empty;
        public InvoiceStatus Value { get; set; }
    }
}
